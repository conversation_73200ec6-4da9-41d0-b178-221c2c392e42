'use client'

import { useState, useEffect } from 'react'

interface NetworkStatus {
  isOnline: boolean
  downlink?: number
  effectiveType?: '2g' | '3g' | '4g' | 'slow-2g'
  saveData?: boolean
  rtt?: number
  type?: 'bluetooth' | 'cellular' | 'ethernet' | 'none' | 'wifi' | 'wimax' | 'other' | 'unknown'
  connectionType: 'slow' | 'medium' | 'fast' | 'unknown'
  quality?: 'excellent' | 'good' | 'fair' | 'poor' | 'offline'
  isFastConnection?: boolean
  isSlowConnection?: boolean
}

interface NavigatorConnection extends EventTarget {
  downlink: number
  effectiveType: '2g' | '3g' | '4g' | 'slow-2g'
  saveData: boolean
  rtt: number
  type?: 'bluetooth' | 'cellular' | 'ethernet' | 'none' | 'wifi' | 'wimax' | 'other' | 'unknown'
  addEventListener(type: 'change', listener: EventListener): void
  removeEventListener(type: 'change', listener: EventListener): void
}

declare global {
  interface Navigator {
    connection?: NavigatorConnection
    mozConnection?: NavigatorConnection
    webkitConnection?: NavigatorConnection
  }
}

export function useNetworkStatus(): NetworkStatus {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,
    connectionType: 'unknown'
  })

  useEffect(() => {
    const getConnectionType = (connection?: NavigatorConnection): 'slow' | 'medium' | 'fast' | 'unknown' => {
      if (!connection) return 'unknown'

      // Use effective type if available
      if (connection.effectiveType) {
        switch (connection.effectiveType) {
          case 'slow-2g':
          case '2g':
            return 'slow'
          case '3g':
            return 'medium'
          case '4g':
            return 'fast'
          default:
            return 'unknown'
        }
      }

      // Fallback to downlink speed
      if (connection.downlink !== undefined) {
        if (connection.downlink < 1.5) return 'slow'
        if (connection.downlink < 10) return 'medium'
        return 'fast'
      }

      return 'unknown'
    }

    const getQuality = (isOnline: boolean, connectionType: string, saveData?: boolean): 'excellent' | 'good' | 'fair' | 'poor' | 'offline' => {
      if (!isOnline) return 'offline'
      if (saveData) return 'poor'

      switch (connectionType) {
        case 'fast': return 'excellent'
        case 'medium': return 'good'
        case 'slow': return 'poor'
        default: return 'fair'
      }
    }

    const updateNetworkStatus = () => {
      const isOnline = navigator.onLine

      // Get connection information if available
      const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection
      const connectionType = getConnectionType(connection)
      const quality = getQuality(isOnline, connectionType, connection?.saveData)

      setNetworkStatus({
        isOnline,
        downlink: connection?.downlink,
        effectiveType: connection?.effectiveType,
        saveData: connection?.saveData,
        rtt: connection?.rtt,
        type: connection?.type,
        connectionType,
        quality,
        isFastConnection: connectionType === 'fast',
        isSlowConnection: connectionType === 'slow'
      })
    }

    // Initial update
    updateNetworkStatus()

    // Listen for online/offline events
    const handleOnline = () => updateNetworkStatus()
    const handleOffline = () => updateNetworkStatus()

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Listen for connection changes if available
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection
    if (connection) {
      connection.addEventListener('change', updateNetworkStatus)
    }

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      
      if (connection) {
        connection.removeEventListener('change', updateNetworkStatus)
      }
    }
  }, [])

  return networkStatus
}

// Helper function to determine if the connection is fast enough for high quality audio
export function isHighQualityConnection(networkStatus: NetworkStatus): boolean {
  if (!networkStatus.isOnline) return false
  
  // Check for save data preference
  if (networkStatus.saveData) return false
  
  // Check effective connection type
  if (networkStatus.effectiveType && ['slow-2g', '2g'].includes(networkStatus.effectiveType)) {
    return false
  }
  
  // Check downlink speed (in Mbps)
  if (networkStatus.downlink && networkStatus.downlink < 1) {
    return false
  }
  
  return true
}

// Helper function to get recommended audio quality based on network
export function getRecommendedAudioQuality(networkStatus: NetworkStatus): 'low' | 'medium' | 'high' {
  if (!networkStatus.isOnline) return 'low'
  
  if (networkStatus.saveData || networkStatus.effectiveType === 'slow-2g') {
    return 'low'
  }
  
  if (networkStatus.effectiveType === '2g' || (networkStatus.downlink && networkStatus.downlink < 0.5)) {
    return 'low'
  }
  
  if (networkStatus.effectiveType === '3g' || (networkStatus.downlink && networkStatus.downlink < 2)) {
    return 'medium'
  }
  
  return 'high'
}

// Utility functions for network-aware features
export const getOptimalAudioQuality = (networkStatus: NetworkStatus): 'high' | 'medium' | 'low' => {
  if (!networkStatus.isOnline) return 'low'
  
  switch (networkStatus.quality) {
    case 'excellent':
    case 'good':
      return 'high'
    case 'fair':
      return 'medium'
    case 'poor':
    case 'offline':
      return 'low'
    default:
      return 'medium'
  }
}

export const shouldPreloadAudio = (networkStatus: NetworkStatus): boolean => {
  return networkStatus.isFastConnection && !networkStatus.saveData
}

export const getBufferSize = (networkStatus: NetworkStatus): number => {
  // Buffer size in seconds
  switch (networkStatus.quality) {
    case 'excellent':
      return 30
    case 'good':
      return 20
    case 'fair':
      return 10
    case 'poor':
      return 5
    default:
      return 10
  }
}

export const shouldShowDataSavingMode = (networkStatus: NetworkStatus): boolean => {
  return networkStatus.saveData || networkStatus.isSlowConnection
}

// Network speed test utility
export const measureNetworkSpeed = async (): Promise<number> => {
  const startTime = performance.now()
  const imageSize = 1024 * 1024 // 1MB test image
  
  try {
    // Use a small image for speed testing
    const testImage = new Image()
    
    return new Promise((resolve) => {
      testImage.onload = () => {
        const endTime = performance.now()
        const duration = (endTime - startTime) / 1000 // Convert to seconds
        const speedMbps = (imageSize * 8) / (duration * 1024 * 1024) // Convert to Mbps
        resolve(speedMbps)
      }
      
      testImage.onerror = () => {
        resolve(0) // Return 0 if test fails
      }
      
      // Use a cache-busting parameter
      testImage.src = `data:image/svg+xml;base64,${btoa('<svg xmlns="http://www.w3.org/2000/svg" width="1024" height="1024"><rect width="100%" height="100%" fill="transparent"/></svg>')}`
    })
  } catch (error) {
    return 0
  }
} 