'use client'

import React, { useState, useCallback, useEffect } from 'react'
import { AlertCircle, CheckCircle, Eye, EyeOff } from 'lucide-react'

interface ValidationRule {
  test: (value: string) => boolean
  message: string
  severity?: 'error' | 'warning'
}

interface FormFieldProps {
  label: string
  name: string
  type?: 'text' | 'email' | 'password' | 'tel' | 'url'
  value: string
  onChange: (value: string) => void
  onBlur?: () => void
  placeholder?: string
  required?: boolean
  disabled?: boolean
  autoComplete?: string
  validationRules?: ValidationRule[]
  showValidation?: boolean
  className?: string
  icon?: React.ReactNode
  helpText?: string
  maxLength?: number
}

export function FormField({
  label,
  name,
  type = 'text',
  value,
  onChange,
  onBlur,
  placeholder,
  required = false,
  disabled = false,
  autoComplete,
  validationRules = [],
  showValidation = false,
  className = '',
  icon,
  helpText,
  maxLength
}: FormFieldProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [isTouched, setIsTouched] = useState(false)
  const [validationResults, setValidationResults] = useState<{
    isValid: boolean
    errors: string[]
    warnings: string[]
  }>({ isValid: true, errors: [], warnings: [] })

  // Validate field
  const validateField = useCallback((fieldValue: string) => {
    const errors: string[] = []
    const warnings: string[] = []

    // Required validation
    if (required && !fieldValue.trim()) {
      errors.push(`${label} is required`)
    }

    // Custom validation rules
    validationRules.forEach(rule => {
      if (!rule.test(fieldValue)) {
        if (rule.severity === 'warning') {
          warnings.push(rule.message)
        } else {
          errors.push(rule.message)
        }
      }
    })

    // Built-in type validation
    if (fieldValue && type === 'email') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(fieldValue)) {
        errors.push('Please enter a valid email address')
      }
    }

    if (fieldValue && type === 'url') {
      try {
        new URL(fieldValue)
      } catch {
        errors.push('Please enter a valid URL')
      }
    }

    if (fieldValue && type === 'tel') {
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
      if (!phoneRegex.test(fieldValue.replace(/[\s\-\(\)]/g, ''))) {
        errors.push('Please enter a valid phone number')
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }, [label, required, validationRules, type])

  // Update validation when value changes
  useEffect(() => {
    if (isTouched || showValidation) {
      setValidationResults(validateField(value))
    }
  }, [value, isTouched, showValidation, validateField])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    if (maxLength && newValue.length > maxLength) return
    onChange(newValue)
  }

  const handleBlur = () => {
    setIsTouched(true)
    onBlur?.()
  }

  const inputType = type === 'password' && showPassword ? 'text' : type
  const hasError = validationResults.errors.length > 0
  const hasWarning = validationResults.warnings.length > 0
  const showValidationState = (isTouched || showValidation) && (hasError || hasWarning)

  const inputClasses = `
    w-full bg-gray-800/50 border rounded-lg px-4 py-3 text-white placeholder-gray-400 
    transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900
    ${icon ? 'pl-11' : ''}
    ${type === 'password' ? 'pr-12' : ''}
    ${hasError ? 'border-red-500 focus:ring-red-500' : 
      hasWarning ? 'border-yellow-500 focus:ring-yellow-500' : 
      'border-gray-600 focus:ring-purple-500'}
    ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
    ${className}
  `.trim()

  return (
    <div className="space-y-2">
      <label 
        htmlFor={name} 
        className="block text-gray-300 text-sm font-medium"
      >
        {label}
        {required && <span className="text-red-400 ml-1" aria-label="required">*</span>}
      </label>

      <div className="relative">
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            {icon}
          </div>
        )}

        <input
          id={name}
          name={name}
          type={inputType}
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          autoComplete={autoComplete}
          required={required}
          maxLength={maxLength}
          className={inputClasses}
          aria-invalid={hasError}
          aria-describedby={`${name}-help ${name}-validation`}
        />

        {type === 'password' && (
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 rounded"
            aria-label={showPassword ? 'Hide password' : 'Show password'}
          >
            {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
          </button>
        )}

        {showValidationState && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {hasError ? (
              <AlertCircle className="w-5 h-5 text-red-400" aria-hidden="true" />
            ) : hasWarning ? (
              <AlertCircle className="w-5 h-5 text-yellow-400" aria-hidden="true" />
            ) : (
              <CheckCircle className="w-5 h-5 text-green-400" aria-hidden="true" />
            )}
          </div>
        )}
      </div>

      {/* Character count */}
      {maxLength && (
        <div className="text-right text-xs text-gray-500">
          {value.length}/{maxLength}
        </div>
      )}

      {/* Help text */}
      {helpText && (
        <p id={`${name}-help`} className="text-sm text-gray-400">
          {helpText}
        </p>
      )}

      {/* Validation messages */}
      {showValidationState && (
        <div id={`${name}-validation`} className="space-y-1" role="alert" aria-live="polite">
          {validationResults.errors.map((error, index) => (
            <p key={`error-${index}`} className="text-sm text-red-400 flex items-center gap-2">
              <AlertCircle className="w-4 h-4 flex-shrink-0" aria-hidden="true" />
              {error}
            </p>
          ))}
          {validationResults.warnings.map((warning, index) => (
            <p key={`warning-${index}`} className="text-sm text-yellow-400 flex items-center gap-2">
              <AlertCircle className="w-4 h-4 flex-shrink-0" aria-hidden="true" />
              {warning}
            </p>
          ))}
        </div>
      )}
    </div>
  )
}

// Common validation rules
export const ValidationRules = {
  email: {
    test: (value: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
    message: 'Please enter a valid email address'
  },
  
  minLength: (min: number) => ({
    test: (value: string) => value.length >= min,
    message: `Must be at least ${min} characters long`
  }),
  
  maxLength: (max: number) => ({
    test: (value: string) => value.length <= max,
    message: `Must be no more than ${max} characters long`
  }),
  
  strongPassword: {
    test: (value: string) => {
      return value.length >= 8 &&
             /[a-z]/.test(value) &&
             /[A-Z]/.test(value) &&
             /\d/.test(value) &&
             /[!@#$%^&*(),.?":{}|<>]/.test(value)
    },
    message: 'Password must contain at least 8 characters, including uppercase, lowercase, number, and special character'
  },
  
  moderatePassword: {
    test: (value: string) => {
      return value.length >= 6 &&
             /[a-z]/.test(value) &&
             /\d/.test(value)
    },
    message: 'Password must be at least 6 characters with a lowercase letter and number'
  },
  
  username: {
    test: (value: string) => /^[a-zA-Z0-9_-]+$/.test(value),
    message: 'Username can only contain letters, numbers, underscores, and hyphens'
  },
  
  noSpaces: {
    test: (value: string) => !/\s/.test(value),
    message: 'Cannot contain spaces'
  },
  
  alphanumeric: {
    test: (value: string) => /^[a-zA-Z0-9]+$/.test(value),
    message: 'Can only contain letters and numbers'
  },
  
  url: {
    test: (value: string) => {
      try {
        new URL(value)
        return true
      } catch {
        return false
      }
    },
    message: 'Please enter a valid URL'
  }
}

// Form validation hook
export function useFormValidation<T extends Record<string, any>>(
  initialValues: T,
  validationSchema: Record<keyof T, ValidationRule[]>
) {
  const [values, setValues] = useState<T>(initialValues)
  const [errors, setErrors] = useState<Record<keyof T, string[]>>({} as Record<keyof T, string[]>)
  const [touched, setTouched] = useState<Record<keyof T, boolean>>({} as Record<keyof T, boolean>)

  const validateField = useCallback((name: keyof T, value: string) => {
    const rules = validationSchema[name] || []
    const fieldErrors: string[] = []

    rules.forEach(rule => {
      if (!rule.test(value)) {
        fieldErrors.push(rule.message)
      }
    })

    setErrors(prev => ({ ...prev, [name]: fieldErrors }))
    return fieldErrors.length === 0
  }, [validationSchema])

  const setValue = useCallback((name: keyof T, value: string) => {
    setValues(prev => ({ ...prev, [name]: value }))
    if (touched[name]) {
      validateField(name, value)
    }
  }, [touched, validateField])

  const setFieldTouched = useCallback((name: keyof T) => {
    setTouched(prev => ({ ...prev, [name]: true }))
    validateField(name, values[name])
  }, [values, validateField])

  const validateAll = useCallback(() => {
    const allErrors: Record<keyof T, string[]> = {} as Record<keyof T, string[]>
    let isValid = true

    Object.keys(validationSchema).forEach(key => {
      const fieldName = key as keyof T
      const fieldErrors: string[] = []
      const rules = validationSchema[fieldName] || []

      rules.forEach(rule => {
        if (!rule.test(values[fieldName])) {
          fieldErrors.push(rule.message)
          isValid = false
        }
      })

      allErrors[fieldName] = fieldErrors
    })

    setErrors(allErrors)
    setTouched(Object.keys(validationSchema).reduce((acc, key) => {
      acc[key as keyof T] = true
      return acc
    }, {} as Record<keyof T, boolean>))

    return isValid
  }, [values, validationSchema])

  const reset = useCallback(() => {
    setValues(initialValues)
    setErrors({} as Record<keyof T, string[]>)
    setTouched({} as Record<keyof T, boolean>)
  }, [initialValues])

  return {
    values,
    errors,
    touched,
    setValue,
    setFieldTouched,
    validateAll,
    reset,
    isValid: Object.values(errors).every(fieldErrors => fieldErrors.length === 0)
  }
}
