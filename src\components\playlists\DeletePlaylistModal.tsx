'use client'

import React from 'react'
import { X, AlertTriangle, Trash2 } from 'lucide-react'
import { usePlaylist } from '@/contexts/PlaylistContext'
import { Playlist } from '@/types/playlist'

interface DeletePlaylistModalProps {
  isOpen: boolean
  onClose: () => void
  playlist: Playlist | null
}

export default function DeletePlaylistModal({ isOpen, onClose, playlist }: DeletePlaylistModalProps) {
  const { deletePlaylist, loading, error } = usePlaylist()

  const handleDelete = async () => {
    if (!playlist) return

    try {
      await deletePlaylist(playlist.id)
      onClose()
    } catch (error) {
      console.error('Failed to delete playlist:', error)
    }
  }

  if (!isOpen || !playlist) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Delete Playlist
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Error Display */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
            </div>
          )}

          {/* Warning Message */}
          <div className="mb-6">
            <p className="text-gray-900 dark:text-white mb-2">
              Are you sure you want to delete <span className="font-semibold">&quot;{playlist.name}&quot;</span>?
            </p>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              This action cannot be undone. The playlist and all its tracks will be permanently removed.
            </p>
          </div>

          {/* Playlist Info */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-red-400 to-red-600 rounded-lg flex items-center justify-center">
                <Trash2 className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">{playlist.name}</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {playlist.track_count || 0} {playlist.track_count === 1 ? 'track' : 'tracks'}
                  {playlist.is_public && ' • Public'}
                </p>
                {playlist.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                    {playlist.description}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleDelete}
              disabled={loading}
              className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white rounded-lg transition-colors disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Deleting...</span>
                </>
              ) : (
                <>
                  <Trash2 className="w-4 h-4" />
                  <span>Delete Playlist</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
} 