'use client'

import { useRef, useEffect, useState, useCallback } from 'react'
import { useAudio } from '@/contexts/AudioContext'
import { useNetworkStatus } from '@/hooks/useNetworkStatus'

export default function GlobalAudioPlayer() {
  const audioRef = useRef<HTMLAudioElement>(null)
  const [retryCount, setRetryCount] = useState(0)
  const [lastError, setLastError] = useState<string | null>(null)
  const networkStatus = useNetworkStatus()

  const {
    currentTrack,
    isPlaying,
    volume,
    isMuted,
    currentTime,
    togglePlayPause,
    nextTrack,
    seek,
    setVolume,
    setCurrentTime,
    setDuration,
    setLoading,
    setError: setAudioError
  } = useAudio()

  const maxRetries = 3

  // Enhanced play function with retry logic
  const playWithRetry = useCallback(async () => {
    if (!audioRef.current) return

    try {
      await audioRef.current.play()
      setRetryCount(0)
      setLastError(null)
      setAudioError(null)
    } catch (error: any) {
      console.error('Audio play error:', error)

      if (retryCount < maxRetries) {
        setRetryCount(prev => prev + 1)
        setTimeout(() => playWithRetry(), 1000 * (retryCount + 1))
      } else {
        const errorMessage = getErrorMessage(error)
        setLastError(errorMessage)
        setAudioError(errorMessage)
        setLoading(false)
      }
    }
  }, [retryCount, maxRetries, setAudioError, setLoading])

  // Get user-friendly error messages
  const getErrorMessage = useCallback((error: any): string => {
    if (!networkStatus.isOnline) {
      return 'No internet connection. Please check your network and try again.'
    }

    const errorCode = error?.target?.error?.code || error?.code

    switch (errorCode) {
      case 1: // MEDIA_ERR_ABORTED
        return 'Playback was aborted. Please try again.'
      case 2: // MEDIA_ERR_NETWORK
        return 'Network error occurred while loading audio. Please check your connection.'
      case 3: // MEDIA_ERR_DECODE
        return 'Audio file is corrupted or in an unsupported format.'
      case 4: // MEDIA_ERR_SRC_NOT_SUPPORTED
        return 'Audio format not supported by your browser.'
      default:
        if (networkStatus.connectionType === 'slow') {
          return 'Slow connection detected. Audio may take longer to load.'
        }
        return 'Unable to play audio. Please try again or contact support.'
    }
  }, [networkStatus])

  // Handle play/pause changes
  useEffect(() => {
    if (!audioRef.current) return

    if (isPlaying) {
      playWithRetry()
    } else {
      audioRef.current.pause()
    }
  }, [isPlaying, playWithRetry])

  // Handle track changes
  useEffect(() => {
    if (!audioRef.current || !currentTrack) return

    const audio = audioRef.current

    // Load new track if different
    if (audio.src !== currentTrack.file_url) {
      setLoading(true)
      setRetryCount(0)
      setLastError(null)
      setAudioError(null)

      audio.pause()
      audio.src = currentTrack.file_url || ''
      audio.load()

      if (isPlaying) {
        playWithRetry()
      }
    }
  }, [currentTrack, isPlaying, setLoading, playWithRetry, setAudioError])

  // Handle volume changes
  useEffect(() => {
    if (!audioRef.current) return
    audioRef.current.volume = isMuted ? 0 : volume
  }, [volume, isMuted])

  // Handle seek changes - sync with audio element when seek is called from context
  useEffect(() => {
    if (!audioRef.current) return
    const audio = audioRef.current
    
    // Only update if there's a significant difference to avoid loops
    if (Math.abs(audio.currentTime - currentTime) > 1) {
      audio.currentTime = currentTime
    }
  }, [currentTime])

  // Audio event handlers
  const handleTimeUpdate = () => {
    if (!audioRef.current) return
    const audio = audioRef.current
    setCurrentTime(audio.currentTime)
  }

  const handleEnded = () => {
    nextTrack()
  }

  const handleLoadedMetadata = () => {
    if (!audioRef.current) return
    const audio = audioRef.current
    setDuration(audio.duration)
    setLoading(false)
  }

  const handleCanPlay = () => {
    setLoading(false)
  }

  const handleLoadStart = () => {
    setLoading(true)
  }

  const handleError = useCallback((e: any) => {
    console.error('Audio playback error:', e)
    const errorMessage = getErrorMessage(e)
    setLastError(errorMessage)
    setAudioError(errorMessage)
    setLoading(false)

    // Try to recover on network errors
    if (e?.target?.error?.code === 2 && retryCount < maxRetries) {
      setTimeout(() => {
        if (audioRef.current && currentTrack) {
          setRetryCount(prev => prev + 1)
          audioRef.current.load()
          if (isPlaying) {
            playWithRetry()
          }
        }
      }, 2000)
    }
  }, [getErrorMessage, setAudioError, setLoading, retryCount, maxRetries, currentTrack, isPlaying, playWithRetry])

  const handleStalled = useCallback(() => {
    console.warn('Audio playback stalled')
    if (networkStatus.connectionType === 'slow') {
      setAudioError('Buffering... Slow connection detected.')
    }
  }, [networkStatus.connectionType, setAudioError])

  const handleWaiting = useCallback(() => {
    setLoading(true)
  }, [setLoading])

  const handleCanPlayThrough = useCallback(() => {
    setLoading(false)
    setAudioError(null)
  }, [setLoading, setAudioError])

  // Media Session API for lock screen controls
  useEffect(() => {
    if ('mediaSession' in navigator && currentTrack) {
      navigator.mediaSession.metadata = new MediaMetadata({
        title: currentTrack.title,
        artist: currentTrack.artist_name,
        artwork: currentTrack.cover_url ? [
          { src: currentTrack.cover_url, sizes: '512x512', type: 'image/jpeg' }
        ] : undefined
      })

      navigator.mediaSession.setActionHandler('play', () => {
        if (!isPlaying) togglePlayPause()
      })

      navigator.mediaSession.setActionHandler('pause', () => {
        if (isPlaying) togglePlayPause()
      })

      navigator.mediaSession.setActionHandler('nexttrack', () => {
        nextTrack()
      })

      navigator.mediaSession.setActionHandler('seekto', (details) => {
        if (details.seekTime !== undefined) {
          seek(details.seekTime)
        }
      })
    }
  }, [currentTrack, isPlaying, togglePlayPause, nextTrack, seek])

  return (
    <audio
      ref={audioRef}
      onTimeUpdate={handleTimeUpdate}
      onEnded={handleEnded}
      onLoadedMetadata={handleLoadedMetadata}
      onCanPlay={handleCanPlay}
      onCanPlayThrough={handleCanPlayThrough}
      onLoadStart={handleLoadStart}
      onWaiting={handleWaiting}
      onStalled={handleStalled}
      onError={handleError}
      preload={networkStatus.connectionType === 'fast' ? 'auto' : 'metadata'}
      crossOrigin="anonymous"
      style={{ display: 'none' }}
      aria-label="Global audio player"
    />
  )
} 