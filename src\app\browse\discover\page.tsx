'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Shu<PERSON>, <PERSON>rk<PERSON>, Dice6, RefreshCw, Play, Grid3X3, List, ChevronDown, Zap } from 'lucide-react'
import MainLayout from '@/components/layout/MainLayout'
import TrackCard from '@/components/tracks/TrackCard'
import { AudioTrack } from '@/types/track'
import { useAudio } from '@/contexts/AudioContext'
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll'

type ViewMode = 'grid' | 'list'
type DiscoveryMode = 'random' | 'genre' | 'mood' | 'similar'

interface Genre {
  id: string
  name: string
  color: string
  icon: string
  description: string
}

interface Mood {
  id: string
  name: string
  color: string
  icon: string
  description: string
}

export default function DiscoverPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { setQueue } = useAudio()

  const [discoveryMode, setDiscoveryMode] = useState<DiscoveryMode>('random')
  const [selectedGenre, setSelectedGenre] = useState<string>('all')
  const [selectedMood, setSelectedMood] = useState<string>('all')
  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  const [tracks, setTracks] = useState<AudioTrack[]>([])
  const [loading, setLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [page, setPage] = useState(1)

  const genres: Genre[] = useMemo(() => [
    { id: 'all', name: 'All Genres', color: 'bg-gradient-to-r from-purple-600 to-blue-600', icon: '🎵', description: 'Mix of all genres' },
    { id: 'electronic', name: 'Electronic', color: 'bg-gradient-to-r from-cyan-500 to-blue-500', icon: '🎛️', description: 'Synthesized beats and digital sounds' },
    { id: 'ambient', name: 'Ambient', color: 'bg-gradient-to-r from-green-500 to-teal-500', icon: '🌊', description: 'Atmospheric and ethereal' },
    { id: 'classical', name: 'Classical', color: 'bg-gradient-to-r from-yellow-500 to-orange-500', icon: '🎼', description: 'Orchestral and traditional' },
    { id: 'jazz', name: 'Jazz', color: 'bg-gradient-to-r from-purple-500 to-pink-500', icon: '🎷', description: 'Smooth and improvisational' },
    { id: 'rock', name: 'Rock', color: 'bg-gradient-to-r from-red-500 to-orange-600', icon: '🎸', description: 'Energetic and powerful' },
    { id: 'pop', name: 'Pop', color: 'bg-gradient-to-r from-pink-500 to-rose-500', icon: '🎤', description: 'Catchy and mainstream' },
    { id: 'hip-hop', name: 'Hip-Hop', color: 'bg-gradient-to-r from-gray-600 to-gray-800', icon: '🎧', description: 'Rhythmic and urban' }
  ], [])

  const moods: Mood[] = useMemo(() => [
    { id: 'all', name: 'All Moods', color: 'bg-gradient-to-r from-purple-600 to-blue-600', icon: '🎭', description: 'Mix of all moods' },
    { id: 'energetic', name: 'Energetic', color: 'bg-gradient-to-r from-orange-500 to-red-500', icon: '⚡', description: 'High energy and upbeat' },
    { id: 'chill', name: 'Chill', color: 'bg-gradient-to-r from-blue-500 to-cyan-500', icon: '😌', description: 'Relaxed and laid-back' },
    { id: 'focus', name: 'Focus', color: 'bg-gradient-to-r from-green-500 to-emerald-500', icon: '🎯', description: 'Perfect for concentration' },
    { id: 'creative', name: 'Creative', color: 'bg-gradient-to-r from-purple-500 to-pink-500', icon: '🎨', description: 'Inspiring and imaginative' },
    { id: 'melancholic', name: 'Melancholic', color: 'bg-gradient-to-r from-gray-500 to-blue-600', icon: '🌧️', description: 'Emotional and introspective' },
    { id: 'uplifting', name: 'Uplifting', color: 'bg-gradient-to-r from-yellow-500 to-orange-500', icon: '☀️', description: 'Positive and motivating' }
  ], [])

  // Load random tracks
  const loadRandomTracks = useCallback(async (reset = false) => {
    setLoading(true)
    
    // Mock API call - replace with actual API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const mockTracks: AudioTrack[] = Array.from({ length: 12 }, (_, i) => {
      const aiTools = ['Suno', 'Udio', 'MusicGen', 'AIVA', 'Custom']
      const genreList = genres.slice(1).map(g => g.id)
      const moodList = moods.slice(1).map(m => m.id)
      
      const selectedGenreFilter = selectedGenre === 'all' ? genreList[Math.floor(Math.random() * genreList.length)] : selectedGenre
      const selectedMoodFilter = selectedMood === 'all' ? moodList[Math.floor(Math.random() * moodList.length)] : selectedMood
      
      return {
        id: `discover-track-${Date.now()}-${i}`,
        title: `Discovery Track ${Math.floor(Math.random() * 1000)}`,
        artist: `Artist ${Math.floor(Math.random() * 100)}`,
        duration: 120 + Math.floor(Math.random() * 180),
        src: `/audio/demo${(i % 3) + 1}.mp3`,
        file_url: `/audio/demo${(i % 3) + 1}.mp3`,
        aiTool: aiTools[Math.floor(Math.random() * aiTools.length)],
        genre: selectedGenreFilter,
        mood: selectedMoodFilter,
        created_at: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString(),
        is_public: true,
        play_count: Math.floor(Math.random() * 5000),
        like_count: Math.floor(Math.random() * 500),
        discovery_score: Math.random() * 100
      }
    })

    if (reset) {
      setTracks(mockTracks)
    } else {
      setTracks(prev => [...prev, ...mockTracks])
    }

    setLoading(false)
  }, [selectedGenre, selectedMood, genres, moods])

  // Handle surprise me button
  const handleSurpriseMe = () => {
    // Randomly select genre and mood
    const randomGenre = genres[Math.floor(Math.random() * genres.length)]
    const randomMood = moods[Math.floor(Math.random() * moods.length)]
    
    setSelectedGenre(randomGenre.id)
    setSelectedMood(randomMood.id)
    setPage(1)
    loadRandomTracks(true)
  }

  // Handle discovery mode change
  const handleDiscoveryModeChange = (mode: DiscoveryMode) => {
    setDiscoveryMode(mode)
    setPage(1)
    loadRandomTracks(true)
  }

  // Handle play all tracks
  const handlePlayAll = () => {
    if (tracks.length > 0) {
      setQueue(tracks)
    }
  }

  // Handle shuffle play
  const handleShuffle = () => {
    if (tracks.length > 0) {
      const shuffled = [...tracks].sort(() => Math.random() - 0.5)
      setQueue(shuffled)
    }
  }

  // Initialize
  useEffect(() => {
    loadRandomTracks(true)
  }, [loadRandomTracks])

  const selectedGenreData = genres.find(g => g.id === selectedGenre) || genres[0]
  const selectedMoodData = moods.find(m => m.id === selectedMood) || moods[0]

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Discover Music</h1>
          <p className="text-gray-400">Find your next favorite AI-generated track</p>
        </div>

        {/* Surprise Me Button */}
        <div className="mb-8">
          <button
            onClick={handleSurpriseMe}
            className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center space-x-3 mx-auto"
          >
            <Sparkles className="w-6 h-6" />
            <span className="text-lg">Surprise Me!</span>
            <Dice6 className="w-6 h-6" />
          </button>
          <p className="text-center text-gray-400 mt-2">Get a random mix of genres and moods</p>
        </div>

        {/* Genre Selection */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-white mb-4 flex items-center space-x-2">
            <span>🎵</span>
            <span>Browse by Genre</span>
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            {genres.map((genre) => (
              <button
                key={genre.id}
                onClick={() => {
                  setSelectedGenre(genre.id)
                  setPage(1)
                  loadRandomTracks(true)
                }}
                className={`${genre.color} p-4 rounded-xl text-white font-medium text-sm hover:scale-105 transition-transform ${
                  selectedGenre === genre.id ? 'ring-2 ring-purple-400 ring-offset-2 ring-offset-gray-900' : ''
                }`}
              >
                <div className="text-2xl mb-2">{genre.icon}</div>
                <div className="truncate">{genre.name}</div>
              </button>
            ))}
          </div>
          <p className="text-gray-400 text-sm mt-2">{selectedGenreData.description}</p>
        </div>

        {/* Mood Selection */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-white mb-4 flex items-center space-x-2">
            <span>🎭</span>
            <span>Browse by Mood</span>
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
            {moods.map((mood) => (
              <button
                key={mood.id}
                onClick={() => {
                  setSelectedMood(mood.id)
                  setPage(1)
                  loadRandomTracks(true)
                }}
                className={`${mood.color} p-4 rounded-xl text-white font-medium text-sm hover:scale-105 transition-transform ${
                  selectedMood === mood.id ? 'ring-2 ring-purple-400 ring-offset-2 ring-offset-gray-900' : ''
                }`}
              >
                <div className="text-2xl mb-2">{mood.icon}</div>
                <div className="truncate">{mood.name}</div>
              </button>
            ))}
          </div>
          <p className="text-gray-400 text-sm mt-2">{selectedMoodData.description}</p>
        </div>

        {/* Current Selection Banner */}
        <div className="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl p-6 mb-8 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="text-4xl">🎲</div>
              <div>
                <h2 className="text-2xl font-bold">Discovery Mix</h2>
                <p className="opacity-90">
                  {selectedGenreData.name} • {selectedMoodData.name}
                </p>
                <div className="flex items-center space-x-4 mt-2 text-sm opacity-80">
                  <span className="flex items-center space-x-1">
                    <Zap className="w-4 h-4" />
                    <span>{tracks.length} tracks discovered</span>
                  </span>
                  <span className="flex items-center space-x-1">
                    <Sparkles className="w-4 h-4" />
                    <span>Personalized for you</span>
                  </span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => loadRandomTracks(true)}
                className="flex items-center space-x-2 bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors"
              >
                <RefreshCw className="w-4 h-4" />
                <span>Refresh</span>
              </button>
              <button
                onClick={handlePlayAll}
                className="flex items-center space-x-2 bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors"
              >
                <Play className="w-4 h-4" />
                <span>Play All</span>
              </button>
              <button
                onClick={handleShuffle}
                className="flex items-center space-x-2 bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors"
              >
                <Shuffle className="w-4 h-4" />
                <span>Shuffle</span>
              </button>
            </div>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <span className="text-gray-400 text-sm">
              {tracks.length} tracks discovered
            </span>
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center space-x-2 bg-gray-800 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'grid' ? 'bg-purple-600 text-white' : 'text-gray-400 hover:text-white'
              }`}
            >
              <Grid3X3 className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'list' ? 'bg-purple-600 text-white' : 'text-gray-400 hover:text-white'
              }`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Tracks Display */}
        {tracks.length > 0 ? (
          <div className={
            viewMode === 'grid' 
              ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
              : 'space-y-3'
          }>
            {tracks.map((track) => (
              <TrackCard
                key={track.id}
                track={track}
                layout={viewMode === 'grid' ? 'vertical' : 'horizontal'}
                size={viewMode === 'grid' ? 'md' : 'sm'}
                showArtwork={true}
                showStats={true}
                showActions={true}
              />
            ))}
          </div>
        ) : loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
            <span className="ml-3 text-gray-400">Discovering tracks...</span>
          </div>
        ) : (
          <div className="text-center py-12">
            <Sparkles className="w-16 h-16 text-gray-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">No tracks discovered yet</h3>
            <p className="text-gray-400">Try the &quot;Surprise Me&quot; button or select different filters.</p>
          </div>
        )}

        {/* Load More Button */}
        <div className="flex justify-center mt-8">
          <button
            onClick={() => loadRandomTracks(false)}
            disabled={loading}
            className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white font-medium py-3 px-6 rounded-lg transition-colors flex items-center space-x-2"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Discovering...</span>
              </>
            ) : (
              <>
                <Sparkles className="w-4 h-4" />
                <span>Discover More</span>
              </>
            )}
          </button>
        </div>
      </div>
    </MainLayout>
  )
} 