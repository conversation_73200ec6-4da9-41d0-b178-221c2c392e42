'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Music, TrendingUp, Clock, Users, Play, Shuffle, Grid3X3, List, ChevronDown } from 'lucide-react'
import MainLayout from '@/components/layout/MainLayout'
import TrackCard from '@/components/tracks/TrackCard'
import { AudioTrack } from '@/types/track'
import { useAudio } from '@/contexts/AudioContext'
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll'

interface AITool {
  id: string
  name: string
  description: string
  color: string
  icon: string
  trackCount: number
  popularityScore: number
}

type ViewMode = 'grid' | 'list'
type SortBy = 'newest' | 'popular' | 'title' | 'duration'

export default function AIToolsPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { setQueue, addToQueue } = useAudio()

  const [selectedTool, setSelectedTool] = useState<string>(searchParams.get('tool') || 'all')
  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  const [sortBy, setSortBy] = useState<SortBy>('newest')
  const [tracks, setTracks] = useState<AudioTrack[]>([])
  const [loading, setLoading] = useState(true)
  const [hasMore, setHasMore] = useState(true)
  const [page, setPage] = useState(1)

  const aiTools: AITool[] = useMemo(() => [
    {
      id: 'all',
      name: 'All AI Tools',
      description: 'Browse tracks from all AI music generators',
      color: 'bg-gradient-to-r from-purple-600 to-blue-600',
      icon: '🎵',
      trackCount: 5420,
      popularityScore: 100
    },
    {
      id: 'Suno',
      name: 'Suno AI',
      description: 'Advanced AI music generation with lyrics and vocals',
      color: 'bg-gradient-to-r from-green-500 to-emerald-600',
      icon: '🎤',
      trackCount: 1850,
      popularityScore: 95
    },
    {
      id: 'Udio',
      name: 'Udio',
      description: 'High-quality AI music with professional sound',
      color: 'bg-gradient-to-r from-blue-500 to-cyan-500',
      icon: '🎧',
      trackCount: 1420,
      popularityScore: 88
    },
    {
      id: 'MusicGen',
      name: 'MusicGen',
      description: 'Meta\'s open-source music generation model',
      color: 'bg-gradient-to-r from-orange-500 to-red-500',
      icon: '🔬',
      trackCount: 980,
      popularityScore: 75
    },
    {
      id: 'AIVA',
      name: 'AIVA',
      description: 'AI composer specializing in classical and cinematic music',
      color: 'bg-gradient-to-r from-indigo-500 to-purple-600',
      icon: '🎼',
      trackCount: 650,
      popularityScore: 70
    },
    {
      id: 'Amper',
      name: 'Amper Music',
      description: 'AI music for content creators and media',
      color: 'bg-gradient-to-r from-pink-500 to-rose-500',
      icon: '📺',
      trackCount: 420,
      popularityScore: 65
    },
    {
      id: 'Mubert',
      name: 'Mubert',
      description: 'Real-time AI music generation and streaming',
      color: 'bg-gradient-to-r from-teal-500 to-green-500',
      icon: '🌊',
      trackCount: 380,
      popularityScore: 60
    },
    {
      id: 'Custom',
      name: 'Custom Models',
      description: 'Tracks from custom and experimental AI models',
      color: 'bg-gradient-to-r from-gray-600 to-gray-800',
      icon: '⚡',
      trackCount: 320,
      popularityScore: 55
    }
  ], [])

  // Update URL when filters change
  const updateURL = useCallback(() => {
    const params = new URLSearchParams()
    if (selectedTool !== 'all') params.set('tool', selectedTool)
    if (sortBy !== 'newest') params.set('sort', sortBy)
    if (viewMode !== 'grid') params.set('view', viewMode)
    
    const newURL = params.toString() ? `/browse/ai-tools?${params.toString()}` : '/browse/ai-tools'
    router.replace(newURL, { scroll: false })
  }, [selectedTool, sortBy, viewMode, router])

  // Load tracks with infinite scroll
  const loadTracks = useCallback(async (pageNum: number, reset = false) => {
    setLoading(true)
    
    // Mock API call - replace with actual API
    await new Promise(resolve => setTimeout(resolve, 800))
    
    const mockTracks: AudioTrack[] = Array.from({ length: 20 }, (_, i) => {
      const toolIndex = Math.floor(Math.random() * (aiTools.length - 1)) + 1
      const tool = aiTools[toolIndex]
      const trackNum = (pageNum - 1) * 20 + i + 1
      
      return {
        id: `track-${pageNum}-${i}`,
        title: `AI Track ${trackNum}`,
        artist: `${tool.name} Artist ${Math.floor(Math.random() * 100)}`,
        duration: 120 + Math.floor(Math.random() * 180),
        src: `/audio/demo${(i % 3) + 1}.mp3`,
        file_url: `/audio/demo${(i % 3) + 1}.mp3`,
        aiTool: tool.id,
        genre: ['electronic', 'ambient', 'classical', 'jazz', 'rock'][Math.floor(Math.random() * 5)],
        created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        is_public: true,
        play_count: Math.floor(Math.random() * 10000),
        like_count: Math.floor(Math.random() * 1000)
      }
    })

    // Filter by selected tool
    const filteredTracks = selectedTool === 'all' 
      ? mockTracks 
      : mockTracks.filter(track => track.aiTool === selectedTool)

    // Sort tracks
    const sortedTracks = [...filteredTracks].sort((a, b) => {
      switch (sortBy) {
        case 'popular':
          return (b.play_count || 0) - (a.play_count || 0)
        case 'title':
          return a.title.localeCompare(b.title)
        case 'duration':
          return (a.duration || 0) - (b.duration || 0)
        case 'newest':
        default:
          return new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime()
      }
    })

    if (reset) {
      setTracks(sortedTracks)
    } else {
      setTracks(prev => [...prev, ...sortedTracks])
    }

    setHasMore(pageNum < 5) // Mock pagination limit
    setLoading(false)
  }, [selectedTool, sortBy, aiTools])

  // Infinite scroll hook
  const { isFetching } = useInfiniteScroll({
    fetchMore: () => {
      if (hasMore && !loading) {
        setPage(prev => prev + 1)
        loadTracks(page + 1)
      }
    },
    hasMore,
    loading
  })

  // Initialize and handle filter changes
  useEffect(() => {
    setPage(1)
    loadTracks(1, true)
    updateURL()
  }, [selectedTool, sortBy, loadTracks, updateURL])

  // Handle tool selection
  const handleToolSelect = (toolId: string) => {
    setSelectedTool(toolId)
    setPage(1)
  }

  // Handle play all tracks
  const handlePlayAll = () => {
    if (tracks.length > 0) {
      setQueue(tracks)
    }
  }

  // Handle shuffle play
  const handleShuffle = () => {
    if (tracks.length > 0) {
      const shuffled = [...tracks].sort(() => Math.random() - 0.5)
      setQueue(shuffled)
    }
  }

  const selectedToolData = aiTools.find(tool => tool.id === selectedTool) || aiTools[0]

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Browse by AI Tool</h1>
          <p className="text-gray-400">Discover tracks created with different AI music generators</p>
        </div>

        {/* AI Tools Grid */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">AI Music Generators</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            {aiTools.map((tool) => (
              <button
                key={tool.id}
                onClick={() => handleToolSelect(tool.id)}
                className={`${tool.color} p-4 rounded-xl text-white font-medium text-sm hover:scale-105 transition-transform ${
                  selectedTool === tool.id ? 'ring-2 ring-purple-400 ring-offset-2 ring-offset-gray-900' : ''
                }`}
              >
                <div className="text-2xl mb-2">{tool.icon}</div>
                <div className="truncate">{tool.name}</div>
                <div className="text-xs opacity-80 mt-1">{tool.trackCount} tracks</div>
              </button>
            ))}
          </div>
        </div>

        {/* Selected Tool Info */}
        <div className={`${selectedToolData.color} rounded-xl p-6 mb-8 text-white`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="text-4xl">{selectedToolData.icon}</div>
              <div>
                <h2 className="text-2xl font-bold">{selectedToolData.name}</h2>
                <p className="opacity-90">{selectedToolData.description}</p>
                <div className="flex items-center space-x-4 mt-2 text-sm opacity-80">
                  <span className="flex items-center space-x-1">
                    <Music className="w-4 h-4" />
                    <span>{selectedToolData.trackCount} tracks</span>
                  </span>
                  <span className="flex items-center space-x-1">
                    <TrendingUp className="w-4 h-4" />
                    <span>{selectedToolData.popularityScore}% popularity</span>
                  </span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={handlePlayAll}
                className="flex items-center space-x-2 bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors"
              >
                <Play className="w-4 h-4" />
                <span>Play All</span>
              </button>
              <button
                onClick={handleShuffle}
                className="flex items-center space-x-2 bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors"
              >
                <Shuffle className="w-4 h-4" />
                <span>Shuffle</span>
              </button>
            </div>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            {/* Sort Dropdown */}
            <div className="relative">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as SortBy)}
                className="bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 appearance-none pr-8"
              >
                <option value="newest">Newest First</option>
                <option value="popular">Most Popular</option>
                <option value="title">Title A-Z</option>
                <option value="duration">Duration</option>
              </select>
              <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
            </div>

            {/* Track Count */}
            <span className="text-gray-400 text-sm">
              {tracks.length} tracks {hasMore && '(loading more...)'}
            </span>
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center space-x-2 bg-gray-800 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'grid' ? 'bg-purple-600 text-white' : 'text-gray-400 hover:text-white'
              }`}
            >
              <Grid3X3 className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'list' ? 'bg-purple-600 text-white' : 'text-gray-400 hover:text-white'
              }`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Tracks Display */}
        {tracks.length > 0 ? (
          <div className={
            viewMode === 'grid' 
              ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
              : 'space-y-3'
          }>
            {tracks.map((track) => (
              <TrackCard
                key={track.id}
                track={track}
                layout={viewMode === 'grid' ? 'vertical' : 'horizontal'}
                size={viewMode === 'grid' ? 'md' : 'sm'}
                showArtwork={true}
                showStats={true}
                showActions={true}
              />
            ))}
          </div>
        ) : loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
            <span className="ml-3 text-gray-400">Loading tracks...</span>
          </div>
        ) : (
          <div className="text-center py-12">
            <Music className="w-16 h-16 text-gray-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">No tracks found</h3>
            <p className="text-gray-400">Try selecting a different AI tool or check back later.</p>
          </div>
        )}

        {/* Loading More Indicator */}
        {isFetching && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-500"></div>
            <span className="ml-3 text-gray-400">Loading more tracks...</span>
          </div>
        )}
      </div>
    </MainLayout>
  )
} 