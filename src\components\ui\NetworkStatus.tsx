'use client'

import React, { useState, useEffect } from 'react'
import { Wifi, WifiOff, AlertTriangle, RefreshCw, Signal, SignalLow, SignalMedium, SignalHigh } from 'lucide-react'
import { useNetworkStatus } from '@/hooks/useNetworkStatus'

interface NetworkStatusProps {
  showDetails?: boolean
  className?: string
  onRetry?: () => void
}

export function NetworkStatusIndicator({ showDetails = false, className = '' }: NetworkStatusProps) {
  const networkStatus = useNetworkStatus()
  const [showTooltip, setShowTooltip] = useState(false)

  if (networkStatus.isOnline && networkStatus.connectionType !== 'slow') {
    return null // Don't show anything when connection is good
  }

  const getSignalIcon = () => {
    if (!networkStatus.isOnline) {
      return <WifiOff className="w-4 h-4 text-red-400" />
    }
    
    switch (networkStatus.connectionType) {
      case 'slow':
        return <SignalLow className="w-4 h-4 text-yellow-400" />
      case 'fast':
        return <SignalHigh className="w-4 h-4 text-green-400" />
      default:
        return <SignalMedium className="w-4 h-4 text-blue-400" />
    }
  }

  const getStatusText = () => {
    if (!networkStatus.isOnline) {
      return 'Offline'
    }
    
    switch (networkStatus.connectionType) {
      case 'slow':
        return 'Slow connection'
      case 'fast':
        return 'Fast connection'
      default:
        return 'Connected'
    }
  }

  const getStatusColor = () => {
    if (!networkStatus.isOnline) {
      return 'bg-red-500/20 border-red-500/30 text-red-400'
    }
    
    switch (networkStatus.connectionType) {
      case 'slow':
        return 'bg-yellow-500/20 border-yellow-500/30 text-yellow-400'
      case 'fast':
        return 'bg-green-500/20 border-green-500/30 text-green-400'
      default:
        return 'bg-blue-500/20 border-blue-500/30 text-blue-400'
    }
  }

  return (
    <div 
      className={`relative inline-flex items-center gap-2 px-3 py-1.5 rounded-lg border ${getStatusColor()} ${className}`}
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      {getSignalIcon()}
      {showDetails && (
        <span className="text-xs font-medium">{getStatusText()}</span>
      )}
      
      {showTooltip && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg border border-gray-700 whitespace-nowrap z-50">
          <div className="text-center">
            <div className="font-medium">{getStatusText()}</div>
            {networkStatus.isOnline && (
              <div className="text-gray-400 mt-1">
                {networkStatus.downlink && `${networkStatus.downlink.toFixed(1)} Mbps`}
                {networkStatus.rtt && ` • ${networkStatus.rtt}ms`}
              </div>
            )}
          </div>
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
        </div>
      )}
    </div>
  )
}

export function OfflineBanner({ onRetry, className = '' }: { onRetry?: () => void; className?: string }) {
  const networkStatus = useNetworkStatus()
  const [isRetrying, setIsRetrying] = useState(false)

  const handleRetry = async () => {
    if (isRetrying) return
    
    setIsRetrying(true)
    try {
      // Force a network check
      await fetch('/api/health', { method: 'HEAD' })
      onRetry?.()
    } catch (error) {
      // Still offline
    } finally {
      setIsRetrying(false)
    }
  }

  if (networkStatus.isOnline) {
    return null
  }

  return (
    <div className={`bg-red-500/10 border border-red-500/20 rounded-lg p-4 ${className}`} role="alert">
      <div className="flex items-start gap-3">
        <WifiOff className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
        <div className="flex-1">
          <h3 className="text-red-400 font-medium mb-1">
            You're offline
          </h3>
          <p className="text-gray-300 text-sm mb-3">
            Check your internet connection. Some features may be limited while offline.
          </p>
          <button
            onClick={handleRetry}
            disabled={isRetrying}
            className="inline-flex items-center gap-2 px-3 py-1.5 bg-red-600 hover:bg-red-700 disabled:bg-red-600/50 text-white text-sm rounded-lg transition-colors disabled:cursor-not-allowed"
          >
            <RefreshCw className={`w-3 h-3 ${isRetrying ? 'animate-spin' : ''}`} />
            {isRetrying ? 'Checking...' : 'Try Again'}
          </button>
        </div>
      </div>
    </div>
  )
}

export function SlowConnectionWarning({ onOptimize, className = '' }: { onOptimize?: () => void; className?: string }) {
  const networkStatus = useNetworkStatus()
  const [dismissed, setDismissed] = useState(false)

  useEffect(() => {
    // Reset dismissed state when connection improves
    if (networkStatus.connectionType !== 'slow') {
      setDismissed(false)
    }
  }, [networkStatus.connectionType])

  if (networkStatus.connectionType !== 'slow' || dismissed) {
    return null
  }

  return (
    <div className={`bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4 ${className}`} role="alert">
      <div className="flex items-start gap-3">
        <AlertTriangle className="w-5 h-5 text-yellow-400 flex-shrink-0 mt-0.5" />
        <div className="flex-1">
          <h3 className="text-yellow-400 font-medium mb-1">
            Slow connection detected
          </h3>
          <p className="text-gray-300 text-sm mb-3">
            Audio quality has been automatically reduced to improve playback. You can manually adjust quality in settings.
          </p>
          <div className="flex gap-2">
            {onOptimize && (
              <button
                onClick={onOptimize}
                className="inline-flex items-center gap-2 px-3 py-1.5 bg-yellow-600 hover:bg-yellow-700 text-white text-sm rounded-lg transition-colors"
              >
                Optimize Settings
              </button>
            )}
            <button
              onClick={() => setDismissed(true)}
              className="px-3 py-1.5 text-yellow-400 hover:text-yellow-300 text-sm transition-colors"
            >
              Dismiss
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export function NetworkErrorRetry({ 
  error, 
  onRetry, 
  className = '',
  showDetails = false 
}: { 
  error: string; 
  onRetry: () => void; 
  className?: string;
  showDetails?: boolean;
}) {
  const [isRetrying, setIsRetrying] = useState(false)

  const handleRetry = async () => {
    if (isRetrying) return
    
    setIsRetrying(true)
    try {
      await onRetry()
    } finally {
      setIsRetrying(false)
    }
  }

  return (
    <div className={`text-center p-6 ${className}`}>
      <div className="flex flex-col items-center space-y-4">
        <div className="p-3 bg-red-500/20 rounded-full">
          <WifiOff className="w-8 h-8 text-red-400" />
        </div>
        
        <div>
          <h3 className="text-lg font-semibold text-white mb-2">
            Connection Error
          </h3>
          <p className="text-gray-400 max-w-md">
            {error || 'Unable to connect to the server. Please check your internet connection and try again.'}
          </p>
          
          {showDetails && (
            <details className="mt-3 text-left">
              <summary className="cursor-pointer text-gray-500 text-sm hover:text-gray-400">
                Technical Details
              </summary>
              <div className="mt-2 p-3 bg-gray-900 rounded text-xs text-gray-400 font-mono">
                <p><strong>Error:</strong> {error}</p>
                <p><strong>Time:</strong> {new Date().toLocaleString()}</p>
                <p><strong>URL:</strong> {window.location.href}</p>
              </div>
            </details>
          )}
        </div>

        <button
          onClick={handleRetry}
          disabled={isRetrying}
          className="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-600/50 text-white rounded-lg font-medium transition-colors disabled:cursor-not-allowed"
        >
          <RefreshCw className={`w-4 h-4 ${isRetrying ? 'animate-spin' : ''}`} />
          {isRetrying ? 'Retrying...' : 'Try Again'}
        </button>
      </div>
    </div>
  )
}

// Hook for managing network-dependent features
export function useNetworkAwareFeatures() {
  const networkStatus = useNetworkStatus()
  
  return {
    // Disable high-bandwidth features on slow connections
    enableHighQualityAudio: networkStatus.isOnline && networkStatus.connectionType !== 'slow',
    enableAutoplay: networkStatus.isOnline,
    enablePreloading: networkStatus.isOnline && networkStatus.connectionType === 'fast',
    maxConcurrentDownloads: networkStatus.connectionType === 'fast' ? 3 : 1,
    
    // Adaptive quality settings
    getRecommendedAudioQuality: () => {
      if (!networkStatus.isOnline) return 'offline'
      if (networkStatus.connectionType === 'slow') return 'low'
      if (networkStatus.connectionType === 'fast') return 'high'
      return 'medium'
    }
  }
}
