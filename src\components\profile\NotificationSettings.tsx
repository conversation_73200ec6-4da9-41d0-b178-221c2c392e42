// Notification Settings Component
'use client'

import { useState } from 'react'
import { ExtendedProfile, NotificationSettings as NotificationSettingsType } from '@/types/profile'

interface NotificationSettingsProps {
  profile: ExtendedProfile
  onUpdate: (updates: Partial<ExtendedProfile>) => Promise<{ success: boolean; error: string | null }>
  loading: boolean
}

export default function NotificationSettings({ profile, onUpdate, loading }: NotificationSettingsProps) {
  const [settings, setSettings] = useState<NotificationSettingsType>({
    email_notifications: profile.email_notifications ?? true,
    push_notifications: profile.push_notifications ?? true,
    marketing_emails: profile.marketing_emails ?? false
  })

  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  const handleSettingChange = (key: keyof NotificationSettingsType, value: boolean) => {
    setSettings(prev => ({ ...prev, [key]: value }))
    setMessage(null)
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      setMessage(null)

      const result = await onUpdate(settings)
      
      if (result.success) {
        setMessage({ type: 'success', text: 'Notification settings updated successfully!' })
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to update settings' })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'An unexpected error occurred' })
    } finally {
      setSaving(false)
    }
  }

  const notificationTypes = [
    {
      key: 'email_notifications' as const,
      title: 'Email Notifications',
      description: 'Receive important updates and activity notifications via email',
      icon: '📧',
      items: [
        'New followers and friend requests',
        'Comments on your tracks',
        'Likes on your content',
        'Playlist collaborations',
        'Security alerts'
      ]
    },
    {
      key: 'push_notifications' as const,
      title: 'Push Notifications',
      description: 'Get real-time notifications in your browser or mobile app',
      icon: '🔔',
      items: [
        'New track uploads from followed users',
        'Live streaming notifications',
        'Playlist updates',
        'Direct messages',
        'System announcements'
      ]
    },
    {
      key: 'marketing_emails' as const,
      title: 'Marketing Emails',
      description: 'Receive newsletters, feature updates, and promotional content',
      icon: '📰',
      items: [
        'Weekly music discovery newsletters',
        'New feature announcements',
        'Special offers and promotions',
        'Community highlights',
        'Platform updates and tips'
      ]
    }
  ]

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Notification Settings
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Choose how and when you want to be notified about activity on Tunami.
        </p>
      </div>

      <div className="space-y-8">
        {notificationTypes.map((type) => (
          <div key={type.key} className="border border-gray-200 dark:border-gray-600 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <span className="text-2xl">{type.icon}</span>
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    {type.title}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {type.description}
                  </p>
                </div>
              </div>
              
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings[type.key]}
                  onChange={(e) => handleSettingChange(type.key, e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 dark:peer-focus:ring-purple-800 rounded-full peer dark:bg-gray-600 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-purple-600"></div>
              </label>
            </div>

            {/* Notification Details */}
            <div className={`transition-all duration-200 ${
              settings[type.key] ? 'opacity-100' : 'opacity-50'
            }`}>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                You&apos;ll receive notifications for:
              </h4>
              <ul className="space-y-1">
                {type.items.map((item, index) => (
                  <li key={index} className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <span className="text-green-500">✓</span>
                    {item}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        ))}

        {/* Notification Frequency */}
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            📅 Notification Frequency
          </h3>
          <div className="space-y-3">
            <label className="flex items-center gap-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
              <input
                type="radio"
                name="frequency"
                value="instant"
                defaultChecked
                className="text-purple-600 focus:ring-purple-500"
              />
              <div>
                <div className="font-medium text-gray-900 dark:text-white">Instant</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Get notified immediately when something happens
                </div>
              </div>
            </label>

            <label className="flex items-center gap-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
              <input
                type="radio"
                name="frequency"
                value="daily"
                className="text-purple-600 focus:ring-purple-500"
              />
              <div>
                <div className="font-medium text-gray-900 dark:text-white">Daily Digest</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Receive a summary of activity once per day
                </div>
              </div>
            </label>

            <label className="flex items-center gap-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
              <input
                type="radio"
                name="frequency"
                value="weekly"
                className="text-purple-600 focus:ring-purple-500"
              />
              <div>
                <div className="font-medium text-gray-900 dark:text-white">Weekly Summary</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Get a weekly roundup of important activity
                </div>
              </div>
            </label>
          </div>
        </div>

        {/* Quiet Hours */}
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            🌙 Quiet Hours
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Set times when you don&apos;t want to receive push notifications
          </p>
          
          <div className="flex items-center gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                From
              </label>
              <input
                type="time"
                defaultValue="22:00"
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                To
              </label>
              <input
                type="time"
                defaultValue="08:00"
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>
        </div>

        {/* Notification Tips */}
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <span className="text-yellow-500 text-xl">💡</span>
            <div>
              <h4 className="font-medium text-yellow-900 dark:text-yellow-300 mb-2">
                Notification Tips
              </h4>
              <ul className="text-sm text-yellow-800 dark:text-yellow-400 space-y-1">
                <li>• You can always change these settings later</li>
                <li>• Push notifications require browser permission</li>
                <li>• Marketing emails can be unsubscribed at any time</li>
                <li>• Security notifications cannot be disabled for your safety</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Message */}
        {message && (
          <div className={`p-4 rounded-lg ${
            message.type === 'success' 
              ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-400'
              : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-400'
          }`}>
            {message.text}
          </div>
        )}

        {/* Save Button */}
        <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={handleSave}
            disabled={saving || loading}
            className="bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white px-6 py-2 rounded-lg font-medium transition-colors"
          >
            {saving ? 'Saving...' : 'Save Notification Settings'}
          </button>
        </div>
      </div>
    </div>
  )
} 