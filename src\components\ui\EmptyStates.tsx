'use client'

import React from 'react'
import { 
  Music, 
  Search, 
  Plus, 
  Heart, 
  PlayCircle, 
  Upload, 
  Users, 
  Wifi, 
  WifiOff,
  AlertCircle,
  RefreshCw,
  Headphones,
  Mic,
  Radio,
  Disc3
} from 'lucide-react'

interface EmptyStateProps {
  icon?: React.ReactNode
  title: string
  description: string
  action?: {
    label: string
    onClick: () => void
    variant?: 'primary' | 'secondary'
    icon?: React.ReactNode
  }
  secondaryAction?: {
    label: string
    onClick: () => void
    icon?: React.ReactNode
  }
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

export function EmptyState({
  icon,
  title,
  description,
  action,
  secondaryAction,
  className = '',
  size = 'md'
}: EmptyStateProps) {
  const sizeClasses = {
    sm: 'py-8 px-4',
    md: 'py-12 px-6',
    lg: 'py-16 px-8'
  }

  const iconSizes = {
    sm: 'w-12 h-12',
    md: 'w-16 h-16',
    lg: 'w-20 h-20'
  }

  const titleSizes = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl'
  }

  return (
    <div className={`text-center ${sizeClasses[size]} ${className}`}>
      {icon && (
        <div className="flex justify-center mb-4">
          <div className={`${iconSizes[size]} text-gray-400 flex items-center justify-center`}>
            {icon}
          </div>
        </div>
      )}
      
      <h3 className={`font-semibold text-white mb-2 ${titleSizes[size]}`}>
        {title}
      </h3>
      
      <p className="text-gray-400 mb-6 max-w-md mx-auto leading-relaxed">
        {description}
      </p>

      {(action || secondaryAction) && (
        <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
          {action && (
            <button
              onClick={action.onClick}
              className={`inline-flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 ${
                action.variant === 'secondary'
                  ? 'bg-gray-700 hover:bg-gray-600 text-white focus:ring-gray-500'
                  : 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white focus:ring-purple-500'
              }`}
              aria-label={action.label}
            >
              {action.icon}
              {action.label}
            </button>
          )}
          
          {secondaryAction && (
            <button
              onClick={secondaryAction.onClick}
              className="inline-flex items-center gap-2 px-4 py-2 text-gray-400 hover:text-white transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 rounded-lg"
              aria-label={secondaryAction.label}
            >
              {secondaryAction.icon}
              {secondaryAction.label}
            </button>
          )}
        </div>
      )}
    </div>
  )
}

// Predefined empty states for common scenarios
export function NoTracksFound({ onUpload, onBrowse }: { onUpload?: () => void; onBrowse?: () => void }) {
  return (
    <EmptyState
      icon={<Music className="w-full h-full" />}
      title="No tracks found"
      description="Start your musical journey by uploading your first AI-generated track or browse our collection."
      action={onUpload ? {
        label: "Upload Track",
        onClick: onUpload,
        icon: <Upload className="w-4 h-4" />
      } : undefined}
      secondaryAction={onBrowse ? {
        label: "Browse Music",
        onClick: onBrowse,
        icon: <Search className="w-4 h-4" />
      } : undefined}
    />
  )
}

export function NoPlaylistsFound({ onCreate, onDiscover }: { onCreate?: () => void; onDiscover?: () => void }) {
  return (
    <EmptyState
      icon={<PlayCircle className="w-full h-full" />}
      title="No playlists yet"
      description="Create your first playlist to organize your favorite tracks and discover new music."
      action={onCreate ? {
        label: "Create Playlist",
        onClick: onCreate,
        icon: <Plus className="w-4 h-4" />
      } : undefined}
      secondaryAction={onDiscover ? {
        label: "Discover Playlists",
        onClick: onDiscover,
        icon: <Search className="w-4 h-4" />
      } : undefined}
    />
  )
}

export function NoSearchResults({ query, onClearSearch }: { query?: string; onClearSearch?: () => void }) {
  return (
    <EmptyState
      icon={<Search className="w-full h-full" />}
      title="No results found"
      description={query ? `No tracks found for "${query}". Try different keywords or browse our featured content.` : "No search results to display."}
      action={onClearSearch ? {
        label: "Clear Search",
        onClick: onClearSearch,
        variant: 'secondary',
        icon: <RefreshCw className="w-4 h-4" />
      } : undefined}
      size="sm"
    />
  )
}

export function NoFavoritesFound({ onBrowse }: { onBrowse?: () => void }) {
  return (
    <EmptyState
      icon={<Heart className="w-full h-full" />}
      title="No favorites yet"
      description="Start liking tracks to build your personal collection of favorite music."
      action={onBrowse ? {
        label: "Discover Music",
        onClick: onBrowse,
        icon: <Headphones className="w-4 h-4" />
      } : undefined}
    />
  )
}

export function OfflineState({ onRetry }: { onRetry?: () => void }) {
  return (
    <EmptyState
      icon={<WifiOff className="w-full h-full" />}
      title="You're offline"
      description="Check your internet connection and try again. Some features may be limited while offline."
      action={onRetry ? {
        label: "Try Again",
        onClick: onRetry,
        icon: <RefreshCw className="w-4 h-4" />
      } : undefined}
    />
  )
}

export function LoadingError({ onRetry, error }: { onRetry?: () => void; error?: string }) {
  return (
    <EmptyState
      icon={<AlertCircle className="w-full h-full" />}
      title="Failed to load"
      description={error || "Something went wrong while loading the content. Please try again."}
      action={onRetry ? {
        label: "Retry",
        onClick: onRetry,
        icon: <RefreshCw className="w-4 h-4" />
      } : undefined}
    />
  )
}

export function NoFollowersFound({ onInvite }: { onInvite?: () => void }) {
  return (
    <EmptyState
      icon={<Users className="w-full h-full" />}
      title="No followers yet"
      description="Share your profile and music to connect with other music lovers in the community."
      action={onInvite ? {
        label: "Invite Friends",
        onClick: onInvite,
        icon: <Plus className="w-4 h-4" />
      } : undefined}
    />
  )
}

export function NoRecentActivity({ onExplore }: { onExplore?: () => void }) {
  return (
    <EmptyState
      icon={<Radio className="w-full h-full" />}
      title="No recent activity"
      description="Start listening to music to see your activity and get personalized recommendations."
      action={onExplore ? {
        label: "Explore Music",
        onClick: onExplore,
        icon: <Disc3 className="w-4 h-4" />
      } : undefined}
      size="sm"
    />
  )
}
