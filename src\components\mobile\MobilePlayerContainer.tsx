'use client'

import { useMobilePlayer } from '@/contexts/MobilePlayerContext'
import EnhancedMobilePlayer from './EnhancedMobilePlayer'
import { useMobileDetection } from '@/hooks/useMobileDetection'

function MobilePlayerContainer() {
  const mobileDetection = useMobileDetection()
  const player = useMobilePlayer()

  // Only show mobile player on mobile devices
  if (!mobileDetection.isMobile || !player.isVisible) {
    return null
  }

  return (
    <EnhancedMobilePlayer
      track={player.currentTrack}
      playlist={player.playlist}
      currentIndex={player.currentIndex}
      isVisible={player.isVisible}
      isExpanded={player.isExpanded}
      autoPlay={false}
      onPlay={player.playTrack}
      onPause={player.pauseTrack}
      onNext={player.nextTrack}
      onPrevious={player.previousTrack}
      onToggleExpanded={player.toggleExpanded}
      onToggleLike={() => {
        // TODO: Implement like functionality
        console.log('Toggle like')
      }}
      onShare={() => {
        // TODO: Implement share functionality
        console.log('Share track')
      }}
      onClose={player.hidePlayer}
    />
  )
}

export default MobilePlayerContainer
export { MobilePlayerContainer }