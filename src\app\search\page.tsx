'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { Search, Music, Users, List, Loader2, AlertCircle } from 'lucide-react'
import SearchBar from '@/components/search/SearchBar'
import SearchFilters from '@/components/search/SearchFilters'
import { SearchService } from '@/lib/search-service'
import { SearchResults, SearchFilters as SearchFiltersType } from '@/types/search'
import { Track } from '@/types/track'
import { Playlist } from '@/types/playlist'
import { useAudio } from '@/contexts/AudioContext'
import Image from 'next/image'
import { formatDuration, formatDate } from '@/utils/format'

type TabType = 'all' | 'tracks' | 'playlists' | 'artists'

export default function SearchPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { playTrack, currentTrack, isPlaying } = useAudio()

  const [query, setQuery] = useState(searchParams.get('q') || '')
  const [activeTab, setActiveTab] = useState<TabType>('all')
  const [results, setResults] = useState<SearchResults | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(false)
  const [filters, setFilters] = useState<SearchFiltersType>({})

  // Parse filters from URL parameters
  const parseFiltersFromURL = useCallback((): SearchFiltersType => {
    const urlFilters: SearchFiltersType = {}
    
    // Parse AI tools
    const aiTools = searchParams.get('ai_tool')
    if (aiTools) {
      urlFilters.ai_tool = aiTools.split(',')
    }
    
    // Parse genres
    const genres = searchParams.get('genre')
    if (genres) {
      urlFilters.genre = genres.split(',')
    }
    
    // Parse duration
    const durationMin = searchParams.get('duration_min')
    if (durationMin) {
      urlFilters.duration_min = parseInt(durationMin)
    }
    
    const durationMax = searchParams.get('duration_max')
    if (durationMax) {
      urlFilters.duration_max = parseInt(durationMax)
    }
    
    // Parse dates
    const dateFrom = searchParams.get('date_from')
    if (dateFrom) {
      urlFilters.date_from = dateFrom
    }
    
    const dateTo = searchParams.get('date_to')
    if (dateTo) {
      urlFilters.date_to = dateTo
    }
    
    // Parse visibility
    const isPublic = searchParams.get('is_public')
    if (isPublic !== null) {
      urlFilters.is_public = isPublic === 'true'
    }
    
    // Parse sorting
    const sortBy = searchParams.get('sort_by')
    if (sortBy) {
      urlFilters.sort_by = sortBy as any
    }
    
    const sortOrder = searchParams.get('sort_order')
    if (sortOrder) {
      urlFilters.sort_order = sortOrder as 'asc' | 'desc'
    }
    
    return urlFilters
  }, [searchParams])

  // Update URL with current filters and query
  const updateURL = useCallback((searchQuery: string, searchFilters: SearchFiltersType) => {
    const params = new URLSearchParams()
    
    // Add query
    if (searchQuery.trim()) {
      params.set('q', searchQuery.trim())
    }
    
    // Add filters
    if (searchFilters.ai_tool && searchFilters.ai_tool.length > 0) {
      params.set('ai_tool', searchFilters.ai_tool.join(','))
    }
    
    if (searchFilters.genre && searchFilters.genre.length > 0) {
      params.set('genre', searchFilters.genre.join(','))
    }
    
    if (searchFilters.duration_min !== undefined) {
      params.set('duration_min', searchFilters.duration_min.toString())
    }
    
    if (searchFilters.duration_max !== undefined) {
      params.set('duration_max', searchFilters.duration_max.toString())
    }
    
    if (searchFilters.date_from) {
      params.set('date_from', searchFilters.date_from)
    }
    
    if (searchFilters.date_to) {
      params.set('date_to', searchFilters.date_to)
    }
    
    if (searchFilters.is_public !== undefined) {
      params.set('is_public', searchFilters.is_public.toString())
    }
    
    if (searchFilters.sort_by) {
      params.set('sort_by', searchFilters.sort_by)
    }
    
    if (searchFilters.sort_order) {
      params.set('sort_order', searchFilters.sort_order)
    }
    
    // Update URL without triggering a page reload
    const newURL = params.toString() ? `/search?${params.toString()}` : '/search'
    router.replace(newURL, { scroll: false })
  }, [router])

  // Perform search
  const performSearch = useCallback(async (
    searchQuery: string, 
    searchFilters: SearchFiltersType = {}, 
    pageNum = 1,
    append = false
  ) => {
    if (!searchQuery.trim()) {
      setResults(null)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const searchResults = await SearchService.search(searchQuery, searchFilters, pageNum, 20)
      
      if (append && results) {
        // Append to existing results for pagination
        setResults({
          ...searchResults,
          tracks: [...results.tracks, ...searchResults.tracks],
          playlists: [...results.playlists, ...searchResults.playlists],
          artists: [...results.artists, ...searchResults.artists]
        })
      } else {
        setResults(searchResults)
      }
      
      setHasMore(searchResults.has_more)
      setPage(pageNum)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Search failed')
      console.error('Search error:', err)
    } finally {
      setLoading(false)
    }
  }, [results])

  // Initialize from URL parameters
  useEffect(() => {
    const urlFilters = parseFiltersFromURL()
    setFilters(urlFilters)
    
    const initialQuery = searchParams.get('q')
    if (initialQuery) {
      setQuery(initialQuery)
      performSearch(initialQuery, urlFilters)
    }
  }, [searchParams, parseFiltersFromURL, performSearch])

  // Handle search
  const handleSearch = (searchQuery: string) => {
    setQuery(searchQuery)
    setPage(1)
    
    // Update URL with query and current filters
    updateURL(searchQuery, filters)
    
    performSearch(searchQuery, filters, 1)
  }

  // Handle filter changes
  const handleFiltersChange = (newFilters: SearchFiltersType) => {
    setFilters(newFilters)
    setPage(1)
    
    // Update URL with current query and new filters
    updateURL(query, newFilters)
    
    if (query.trim()) {
      performSearch(query, newFilters, 1)
    }
  }

  // Clear filters
  const clearFilters = () => {
    const emptyFilters = {}
    setFilters(emptyFilters)
    setPage(1)
    
    // Update URL with current query and empty filters
    updateURL(query, emptyFilters)
    
    if (query.trim()) {
      performSearch(query, emptyFilters, 1)
    }
  }

  // Load more results
  const loadMore = () => {
    if (hasMore && !loading && query.trim()) {
      performSearch(query, filters, page + 1, true)
    }
  }

  // Handle track play
  const handleTrackPlay = async (track: Track, index: number) => {
    try {
      await SearchService.trackResultClick(query, 'track', track.id, index)
      playTrack(track)
    } catch (error) {
      console.error('Error tracking click:', error)
      playTrack(track)
    }
  }

  // Get tab counts
  const getTabCounts = () => {
    if (!results) return { tracks: 0, playlists: 0, artists: 0 }
    return {
      tracks: results.total_tracks,
      playlists: results.total_playlists,
      artists: results.total_artists
    }
  }

  const tabCounts = getTabCounts()

  // Filter results based on active tab
  const getFilteredResults = () => {
    if (!results) return { tracks: [], playlists: [], artists: [] }
    
    switch (activeTab) {
      case 'tracks':
        return { tracks: results.tracks, playlists: [], artists: [] }
      case 'playlists':
        return { tracks: [], playlists: results.playlists, artists: [] }
      case 'artists':
        return { tracks: [], playlists: [], artists: results.artists }
      default:
        return results
    }
  }

  const filteredResults = getFilteredResults()

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
            Search Music
          </h1>
          
          {/* Search Bar */}
          <div className="flex flex-col lg:flex-row gap-4 mb-6">
            <div className="flex-1 max-w-2xl">
              <SearchBar
                placeholder="Search tracks, artists, playlists..."
                onSearch={handleSearch}
                className="w-full"
              />
            </div>
            <div className="flex-shrink-0">
              <SearchFilters
                filters={filters}
                onFiltersChange={handleFiltersChange}
                onClearFilters={clearFilters}
              />
            </div>
          </div>

          {/* Search Info */}
          {query && results && (
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Found {results.total_tracks + results.total_playlists + results.total_artists} results for &quot;{query}&quot;
            </div>
          )}
        </div>

        {/* Results */}
        {query.trim() ? (
          <>
            {/* Tabs */}
            {results && (
              <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
                <nav className="flex space-x-8">
                  <button
                    onClick={() => setActiveTab('all')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === 'all'
                        ? 'border-purple-500 text-purple-600 dark:text-purple-400'
                        : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                    }`}
                  >
                    All Results
                  </button>
                  <button
                    onClick={() => setActiveTab('tracks')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors flex items-center space-x-2 ${
                      activeTab === 'tracks'
                        ? 'border-purple-500 text-purple-600 dark:text-purple-400'
                        : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                    }`}
                  >
                    <Music className="w-4 h-4" />
                    <span>Tracks</span>
                    {tabCounts.tracks > 0 && (
                      <span className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-0.5 rounded-full text-xs">
                        {tabCounts.tracks}
                      </span>
                    )}
                  </button>
                  <button
                    onClick={() => setActiveTab('playlists')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors flex items-center space-x-2 ${
                      activeTab === 'playlists'
                        ? 'border-purple-500 text-purple-600 dark:text-purple-400'
                        : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                    }`}
                  >
                    <List className="w-4 h-4" />
                    <span>Playlists</span>
                    {tabCounts.playlists > 0 && (
                      <span className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-0.5 rounded-full text-xs">
                        {tabCounts.playlists}
                      </span>
                    )}
                  </button>
                  <button
                    onClick={() => setActiveTab('artists')}
                    className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors flex items-center space-x-2 ${
                      activeTab === 'artists'
                        ? 'border-purple-500 text-purple-600 dark:text-purple-400'
                        : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                    }`}
                  >
                    <Users className="w-4 h-4" />
                    <span>Artists</span>
                    {tabCounts.artists > 0 && (
                      <span className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-0.5 rounded-full text-xs">
                        {tabCounts.artists}
                      </span>
                    )}
                  </button>
                </nav>
              </div>
            )}

            {/* Loading State */}
            {loading && !results && (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="w-8 h-8 animate-spin text-purple-600" />
                <span className="ml-2 text-gray-600 dark:text-gray-400">Searching...</span>
              </div>
            )}

            {/* Error State */}
            {error && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
                <div className="flex items-center space-x-2 text-red-800 dark:text-red-400">
                  <AlertCircle className="w-5 h-5" />
                  <span className="font-medium">Search Error</span>
                </div>
                <p className="text-red-700 dark:text-red-300 mt-1">{error}</p>
              </div>
            )}

            {/* Results Content */}
            {results && !loading && (
              <div className="space-y-6">
                {/* Tracks */}
                {filteredResults.tracks.length > 0 && (
                  <div>
                    {activeTab === 'all' && (
                      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        Tracks
                      </h2>
                    )}
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
                      {filteredResults.tracks.map((track, index) => (
                        <div
                          key={track.id}
                          className="flex items-center space-x-4 p-4 hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-700 last:border-b-0 cursor-pointer"
                          onClick={() => handleTrackPlay(track, index)}
                        >
                          {/* Cover Image */}
                          <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-blue-500 rounded-lg overflow-hidden flex-shrink-0">
                            {track.cover_image_url ? (
                              <Image
                                src={track.cover_image_url}
                                alt={track.title}
                                width={48}
                                height={48}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <Music className="w-6 h-6 text-white" />
                              </div>
                            )}
                          </div>

                          {/* Track Info */}
                          <div className="flex-1 min-w-0">
                            <h3 className="font-medium text-gray-900 dark:text-white truncate">
                              {track.title}
                            </h3>
                            <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                              {track.artist}
                            </p>
                          </div>

                          {/* Metadata */}
                          <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                            {track.ai_tool && (
                              <span className="bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 px-2 py-1 rounded-full text-xs">
                                {track.ai_tool}
                              </span>
                            )}
                            {track.duration && (
                              <span>{formatDuration(track.duration)}</span>
                            )}
                            <span>{formatDate(track.created_at)}</span>
                          </div>

                          {/* Playing Indicator */}
                          {currentTrack?.id === track.id && isPlaying && (
                            <div className="flex items-center space-x-1 text-purple-600">
                              <div className="w-1 h-4 bg-purple-600 rounded animate-pulse"></div>
                              <div className="w-1 h-4 bg-purple-600 rounded animate-pulse delay-75"></div>
                              <div className="w-1 h-4 bg-purple-600 rounded animate-pulse delay-150"></div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Playlists */}
                {filteredResults.playlists.length > 0 && (
                  <div>
                    {activeTab === 'all' && (
                      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        Playlists
                      </h2>
                    )}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {filteredResults.playlists.map((playlist) => (
                        <div
                          key={playlist.id}
                          className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow cursor-pointer"
                          onClick={() => router.push(`/playlist/${playlist.id}`)}
                        >
                          <div className="flex items-start space-x-3">
                            <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-lg overflow-hidden flex-shrink-0">
                              {playlist.cover_image_url ? (
                                <Image
                                  src={playlist.cover_image_url}
                                  alt={playlist.name}
                                  width={64}
                                  height={64}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center">
                                  <List className="w-8 h-8 text-white" />
                                </div>
                              )}
                            </div>
                            <div className="flex-1 min-w-0">
                              <h3 className="font-medium text-gray-900 dark:text-white truncate">
                                {playlist.name}
                              </h3>
                              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">
                                {playlist.description || `${playlist.track_count || 0} tracks`}
                              </p>
                              <p className="text-xs text-gray-400 dark:text-gray-500 mt-2">
                                Updated {formatDate(playlist.updated_at)}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Artists */}
                {filteredResults.artists.length > 0 && (
                  <div>
                    {activeTab === 'all' && (
                      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        Artists
                      </h2>
                    )}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {filteredResults.artists.map((artist) => (
                        <div
                          key={artist.id}
                          className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow cursor-pointer"
                          onClick={() => router.push(`/artist/${artist.id}`)}
                        >
                          <div className="flex items-center space-x-3">
                            <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-blue-500 rounded-full overflow-hidden flex-shrink-0">
                              {artist.avatar_url ? (
                                <Image
                                  src={artist.avatar_url}
                                  alt={artist.name}
                                  width={64}
                                  height={64}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center">
                                  <Users className="w-8 h-8 text-white" />
                                </div>
                              )}
                            </div>
                            <div className="flex-1 min-w-0">
                              <h3 className="font-medium text-gray-900 dark:text-white truncate">
                                {artist.name}
                              </h3>
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                {artist.track_count} tracks
                              </p>
                              {artist.genres && artist.genres.length > 0 && (
                                <div className="flex flex-wrap gap-1 mt-2">
                                  {artist.genres.slice(0, 2).map((genre) => (
                                    <span
                                      key={genre}
                                      className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-0.5 rounded-full text-xs"
                                    >
                                      {genre}
                                    </span>
                                  ))}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Load More Button */}
                {hasMore && (
                  <div className="text-center">
                    <button
                      onClick={loadMore}
                      disabled={loading}
                      className="inline-flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white px-6 py-3 rounded-lg transition-colors"
                    >
                      {loading ? (
                        <>
                          <Loader2 className="w-4 h-4 animate-spin" />
                          <span>Loading...</span>
                        </>
                      ) : (
                        <span>Load More</span>
                      )}
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* No Results */}
            {results && !loading && 
             results.total_tracks === 0 && 
             results.total_playlists === 0 && 
             results.total_artists === 0 && (
              <div className="text-center py-12">
                <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  No results found
                </h3>
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  Try adjusting your search terms or filters
                </p>
                <button
                  onClick={clearFilters}
                  className="text-purple-600 hover:text-purple-700 font-medium"
                >
                  Clear filters
                </button>
              </div>
            )}
          </>
        ) : (
          /* Empty State */
          <div className="text-center py-12">
            <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Start searching
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              Search for tracks, artists, playlists, and more
            </p>
          </div>
        )}
      </div>
    </div>
  )
} 