'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { Heart, Play, Users, Music, Share2, MessageSquare } from 'lucide-react'
import { getPublicProfile, followUser, unfollowUser, getPublicPlaylists, getUserActivity } from '@/lib/social'
import { createBrowserClient } from '@/lib/supabase'
import type { PublicProfile, Track, Playlist, ActivityWithDetails } from '@/types/database'
import { Avatar } from '@/components/ui/Avatar'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tabs'

const supabase = createBrowserClient()

export default function UserProfilePage() {
  const { username } = useParams()
  const [profile, setProfile] = useState<PublicProfile | null>(null)
  const [tracks, setTracks] = useState<Track[]>([])
  const [playlists, setPlaylists] = useState<Playlist[]>([])
  const [activities, setActivities] = useState<ActivityWithDetails[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isFollowing, setIsFollowing] = useState(false)
  const [currentUser, setCurrentUser] = useState<any>(null)

  useEffect(() => {
    loadUserProfile()
    getCurrentUser()
  }, [username])

  async function getCurrentUser() {
    const { data: { user } } = await supabase.auth.getUser()
    setCurrentUser(user)
  }

  async function loadUserProfile() {
    try {
      setIsLoading(true)
      
      // Get user by username
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('*')
        .eq('username', username)
        .eq('is_public', true)
        .single()

      if (userError || !userData) {
        console.error('User not found')
        return
      }

      // Get full profile with follow status
      const profileData = await getPublicProfile(userData.id)
      setProfile(profileData)
      setIsFollowing(profileData?.is_following || false)

      // Load user's public tracks
      const { data: tracksData } = await supabase
        .from('tracks')
        .select('*')
        .eq('uploaded_by', userData.id)
        .eq('is_public', true)
        .order('created_at', { ascending: false })
        .limit(20)

      setTracks(tracksData || [])

      // Load public playlists
      const { data: playlistsData } = await getPublicPlaylists(userData.id)
      setPlaylists(playlistsData || [])

      // Load user activities
      const activitiesData = await getUserActivity(userData.id)
      setActivities(activitiesData)

    } catch (error) {
      console.error('Error loading profile:', error)
    } finally {
      setIsLoading(false)
    }
  }

  async function handleFollow() {
    if (!profile) return

    try {
      if (isFollowing) {
        await unfollowUser(profile.id)
        setIsFollowing(false)
        setProfile(prev => prev ? { 
          ...prev, 
          followers_count: prev.followers_count - 1 
        } : null)
      } else {
        await followUser(profile.id)
        setIsFollowing(true)
        setProfile(prev => prev ? { 
          ...prev, 
          followers_count: prev.followers_count + 1 
        } : null)
      }
    } catch (error) {
      console.error('Error updating follow status:', error)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
        <div className="container mx-auto px-6 py-8">
          <div className="animate-pulse">
            <div className="h-48 bg-gray-700 rounded-lg mb-6"></div>
            <div className="h-8 bg-gray-700 rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-gray-700 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-white mb-4">User Not Found</h1>
          <p className="text-gray-300">This user profile doesn&apos;t exist or is private.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <div className="container mx-auto px-6 py-8">
        {/* Profile Header */}
        <Card className="bg-gray-800/50 backdrop-blur-sm border-gray-700 p-8 mb-8">
          <div className="flex flex-col md:flex-row items-start gap-6">
            <Avatar
              src={profile.avatar_url}
              alt={profile.full_name || profile.username || ''}
              size="xl"
            />
            
            <div className="flex-1">
              <div className="flex items-center gap-4 mb-4">
                <h1 className="text-3xl font-bold text-white">
                  {profile.full_name || profile.username}
                </h1>
                {profile.username && (
                  <span className="text-gray-400">@{profile.username}</span>
                )}
              </div>

              {profile.bio && (
                <p className="text-gray-300 mb-4 max-w-2xl">{profile.bio}</p>
              )}

              <div className="flex items-center gap-6 mb-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{tracks.length}</div>
                  <div className="text-gray-400 text-sm">Tracks</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{profile.followers_count}</div>
                  <div className="text-gray-400 text-sm">Followers</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{profile.following_count}</div>
                  <div className="text-gray-400 text-sm">Following</div>
                </div>
              </div>

              {currentUser && currentUser.id !== profile.id && (
                <div className="flex gap-3">
                  <Button
                    onClick={handleFollow}
                    variant={isFollowing ? "outline" : "default"}
                    className="flex items-center gap-2"
                  >
                    <Users className="w-4 h-4" />
                    {isFollowing ? 'Unfollow' : 'Follow'}
                  </Button>
                  <Button variant="outline" className="flex items-center gap-2">
                    <MessageSquare className="w-4 h-4" />
                    Message
                  </Button>
                </div>
              )}
            </div>
          </div>
        </Card>

        {/* Content Tabs */}
        <Tabs defaultValue="tracks" className="space-y-6">
          <TabsList className="bg-gray-800/50 border-gray-700">
            <TabsTrigger value="tracks" className="flex items-center gap-2">
              <Music className="w-4 h-4" />
              Tracks ({tracks.length})
            </TabsTrigger>
            <TabsTrigger value="playlists" className="flex items-center gap-2">
              <Play className="w-4 h-4" />
              Playlists ({playlists.length})
            </TabsTrigger>
            <TabsTrigger value="activity" className="flex items-center gap-2">
              <Heart className="w-4 h-4" />
              Activity
            </TabsTrigger>
          </TabsList>

          <TabsContent value="tracks" className="space-y-4">
            {tracks.length > 0 ? (
              <div className="grid gap-4">
                {tracks.map((track) => (
                  <TrackCard key={track.id} track={track} />
                ))}
              </div>
            ) : (
              <Card className="bg-gray-800/50 border-gray-700 p-8 text-center">
                <Music className="w-12 h-12 text-gray-500 mx-auto mb-4" />
                <p className="text-gray-400">No public tracks yet</p>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="playlists" className="space-y-4">
            {playlists.length > 0 ? (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {playlists.map((playlist) => (
                  <PlaylistCard key={playlist.id} playlist={playlist} />
                ))}
              </div>
            ) : (
              <Card className="bg-gray-800/50 border-gray-700 p-8 text-center">
                <Play className="w-12 h-12 text-gray-500 mx-auto mb-4" />
                <p className="text-gray-400">No public playlists yet</p>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="activity" className="space-y-4">
            {activities.length > 0 ? (
              <div className="space-y-3">
                {activities.map((activity) => (
                  <ActivityCard key={activity.id} activity={activity} />
                ))}
              </div>
            ) : (
              <Card className="bg-gray-800/50 border-gray-700 p-8 text-center">
                <Heart className="w-12 h-12 text-gray-500 mx-auto mb-4" />
                <p className="text-gray-400">No recent activity</p>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

function TrackCard({ track }: { track: Track }) {
  return (
    <Card className="bg-gray-800/50 border-gray-700 p-4 hover:bg-gray-700/50 transition-colors">
      <div className="flex items-center gap-4">
        <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
          <Music className="w-6 h-6 text-white" />
        </div>
        
        <div className="flex-1">
          <h3 className="font-semibold text-white">{track.title}</h3>
          <p className="text-gray-400 text-sm">{track.artist_name}</p>
          {track.description && (
            <p className="text-gray-500 text-sm mt-1 line-clamp-2">{track.description}</p>
          )}
        </div>

        <div className="flex items-center gap-2 text-gray-400 text-sm">
          <Play className="w-4 h-4" />
          {track.play_count}
          <Heart className="w-4 h-4" />
          {track.like_count}
        </div>
      </div>
    </Card>
  )
}

function PlaylistCard({ playlist }: { playlist: Playlist }) {
  return (
    <Card className="bg-gray-800/50 border-gray-700 p-4 hover:bg-gray-700/50 transition-colors">
      <div className="aspect-square bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg mb-3 flex items-center justify-center">
        {playlist.cover_image_url ? (
          <img 
            src={playlist.cover_image_url} 
            alt={playlist.name}
            className="w-full h-full object-cover rounded-lg"
          />
        ) : (
          <Play className="w-12 h-12 text-white" />
        )}
      </div>
      
      <h3 className="font-semibold text-white mb-1">{playlist.name}</h3>
      {playlist.description && (
        <p className="text-gray-400 text-sm mb-2 line-clamp-2">{playlist.description}</p>
      )}
      <p className="text-gray-500 text-sm">{playlist.track_count} tracks</p>
    </Card>
  )
}

function ActivityCard({ activity }: { activity: ActivityWithDetails }) {
  const getActivityMessage = () => {
    switch (activity.activity_type) {
      case 'track_upload':
        return `uploaded a new track "${activity.track_title}"`
      case 'playlist_create':
        return `created a new playlist "${activity.playlist_name}"`
      case 'track_like':
        return `liked "${activity.track_title}"`
      case 'user_follow':
        return `started following ${activity.target_user_name || activity.target_user_username}`
      case 'track_mention':
        return `mentioned ${activity.target_user_name || activity.target_user_username} in "${activity.track_title}"`
      case 'playlist_share':
        return `shared playlist "${activity.playlist_name}"`
      default:
        return 'had some activity'
    }
  }

  return (
    <Card className="bg-gray-800/50 border-gray-700 p-4">
      <div className="flex items-center gap-3">
        <Avatar
          src={activity.user_avatar_url}
          alt={activity.user_name || activity.user_username || ''}
          size="sm"
        />
        
        <div className="flex-1">
          <p className="text-gray-300">
            <span className="font-semibold text-white">
              {activity.user_name || activity.user_username}
            </span>{' '}
            {getActivityMessage()}
          </p>
          <p className="text-gray-500 text-sm">
            {new Date(activity.created_at).toLocaleDateString()}
          </p>
        </div>
      </div>
    </Card>
  )
} 