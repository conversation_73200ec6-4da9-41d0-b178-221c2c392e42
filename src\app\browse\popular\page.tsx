'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { TrendingUp, Flame, Crown, Play, Shuffle, Grid3X3, List, ChevronDown, BarChart3 } from 'lucide-react'
import MainLayout from '@/components/layout/MainLayout'
import TrackCard from '@/components/tracks/TrackCard'
import { AudioTrack } from '@/types/track'
import { useAudio } from '@/contexts/AudioContext'
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll'

type ViewMode = 'grid' | 'list'
type TimeFilter = 'today' | 'week' | 'month' | 'all'
type SortBy = 'plays' | 'likes' | 'trending' | 'newest'

export default function PopularTracksPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { setQueue } = useAudio()

  const [timeFilter, setTimeFilter] = useState<TimeFilter>((searchParams.get('time') as TimeFilter) || 'week')
  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  const [sortBy, setSortBy] = useState<SortBy>('plays')
  const [tracks, setTracks] = useState<AudioTrack[]>([])
  const [loading, setLoading] = useState(true)
  const [hasMore, setHasMore] = useState(true)
  const [page, setPage] = useState(1)

  const timeFilters = [
    { value: 'today', label: 'Today', description: 'Most popular tracks today', icon: '🔥' },
    { value: 'week', label: 'This Week', description: 'Top tracks this week', icon: '📈' },
    { value: 'month', label: 'This Month', description: 'Monthly chart toppers', icon: '👑' },
    { value: 'all', label: 'All Time', description: 'Greatest hits of all time', icon: '⭐' }
  ]

  // Update URL when filters change
  const updateURL = useCallback(() => {
    const params = new URLSearchParams()
    if (timeFilter !== 'week') params.set('time', timeFilter)
    if (sortBy !== 'plays') params.set('sort', sortBy)
    if (viewMode !== 'grid') params.set('view', viewMode)
    
    const newURL = params.toString() ? `/browse/popular?${params.toString()}` : '/browse/popular'
    router.replace(newURL, { scroll: false })
  }, [timeFilter, sortBy, viewMode, router])

  // Load tracks with infinite scroll
  const loadTracks = useCallback(async (pageNum: number, reset = false) => {
    setLoading(true)
    
    // Mock API call - replace with actual API
    await new Promise(resolve => setTimeout(resolve, 800))
    
    const getPopularityMultiplier = () => {
      switch (timeFilter) {
        case 'today': return 0.1
        case 'week': return 0.5
        case 'month': return 1
        default: return 2
      }
    }

    const popularityMultiplier = getPopularityMultiplier()
    
    const mockTracks: AudioTrack[] = Array.from({ length: 20 }, (_, i) => {
      const trackNum = (pageNum - 1) * 20 + i + 1
      const basePopularity = Math.floor(Math.random() * 50000) * popularityMultiplier
      
      const aiTools = ['Suno', 'Udio', 'MusicGen', 'AIVA', 'Custom']
      const genres = ['electronic', 'ambient', 'classical', 'jazz', 'rock', 'pop', 'hip-hop']
      
      return {
        id: `popular-track-${pageNum}-${i}`,
        title: `Popular Track ${trackNum}`,
        artist: `Artist ${Math.floor(Math.random() * 100)}`,
        duration: 120 + Math.floor(Math.random() * 180),
        src: `/audio/demo${(i % 3) + 1}.mp3`,
        file_url: `/audio/demo${(i % 3) + 1}.mp3`,
        aiTool: aiTools[Math.floor(Math.random() * aiTools.length)],
        genre: genres[Math.floor(Math.random() * genres.length)],
        created_at: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString(),
        is_public: true,
        play_count: Math.floor(basePopularity + Math.random() * 10000),
        like_count: Math.floor((basePopularity + Math.random() * 10000) * 0.1),
        trending_score: Math.random() * 100
      }
    })

    // Sort tracks
    const sortedTracks = [...mockTracks].sort((a, b) => {
      switch (sortBy) {
        case 'likes':
          return (b.like_count || 0) - (a.like_count || 0)
        case 'trending':
          return (b.trending_score || 0) - (a.trending_score || 0)
        case 'newest':
          return new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime()
        case 'plays':
        default:
          return (b.play_count || 0) - (a.play_count || 0)
      }
    })

    if (reset) {
      setTracks(sortedTracks)
    } else {
      setTracks(prev => [...prev, ...sortedTracks])
    }

    setHasMore(pageNum < 5) // Mock pagination limit
    setLoading(false)
  }, [timeFilter, sortBy])

  // Infinite scroll hook
  const { isFetching } = useInfiniteScroll({
    fetchMore: () => {
      if (hasMore && !loading) {
        setPage(prev => prev + 1)
        loadTracks(page + 1)
      }
    },
    hasMore,
    loading
  })

  // Initialize and handle filter changes
  useEffect(() => {
    setPage(1)
    loadTracks(1, true)
    updateURL()
  }, [timeFilter, sortBy, loadTracks, updateURL])

  // Handle play all tracks
  const handlePlayAll = () => {
    if (tracks.length > 0) {
      setQueue(tracks)
    }
  }

  // Handle shuffle play
  const handleShuffle = () => {
    if (tracks.length > 0) {
      const shuffled = [...tracks].sort(() => Math.random() - 0.5)
      setQueue(shuffled)
    }
  }

  const selectedTimeFilter = timeFilters.find(filter => filter.value === timeFilter) || timeFilters[1]

  // Get top 3 tracks for featured section
  const topTracks = tracks.slice(0, 3)

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Popular Tracks</h1>
          <p className="text-gray-400">Discover the most played AI-generated music</p>
        </div>

        {/* Time Filter Tabs */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2 mb-4">
            {timeFilters.map((filter) => (
              <button
                key={filter.value}
                onClick={() => setTimeFilter(filter.value as TimeFilter)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                  timeFilter === filter.value
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                }`}
              >
                <span>{filter.icon}</span>
                <span>{filter.label}</span>
              </button>
            ))}
          </div>
          <p className="text-gray-400 text-sm">{selectedTimeFilter.description}</p>
        </div>

        {/* Top 3 Featured Tracks */}
        {topTracks.length >= 3 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center space-x-2">
              <Crown className="w-5 h-5 text-yellow-500" />
              <span>Top 3 {selectedTimeFilter.label}</span>
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {topTracks.map((track, index) => (
                <div key={track.id} className="relative">
                  <div className={`absolute -top-2 -left-2 w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm z-10 ${
                    index === 0 ? 'bg-yellow-500' : index === 1 ? 'bg-gray-400' : 'bg-orange-600'
                  }`}>
                    {index + 1}
                  </div>
                  <TrackCard
                    track={track}
                    layout="vertical"
                    size="md"
                    showArtwork={true}
                    showStats={true}
                    showActions={true}
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Stats Banner */}
        <div className="bg-gradient-to-r from-orange-600 to-red-600 rounded-xl p-6 mb-8 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="text-4xl">{selectedTimeFilter.icon}</div>
              <div>
                <h2 className="text-2xl font-bold">{selectedTimeFilter.label} Charts</h2>
                <p className="opacity-90">{selectedTimeFilter.description}</p>
                <div className="flex items-center space-x-4 mt-2 text-sm opacity-80">
                  <span className="flex items-center space-x-1">
                    <BarChart3 className="w-4 h-4" />
                    <span>{tracks.length} tracks loaded</span>
                  </span>
                  <span className="flex items-center space-x-1">
                    <TrendingUp className="w-4 h-4" />
                    <span>Trending now</span>
                  </span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={handlePlayAll}
                className="flex items-center space-x-2 bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors"
              >
                <Play className="w-4 h-4" />
                <span>Play All</span>
              </button>
              <button
                onClick={handleShuffle}
                className="flex items-center space-x-2 bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors"
              >
                <Shuffle className="w-4 h-4" />
                <span>Shuffle</span>
              </button>
            </div>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            {/* Sort Dropdown */}
            <div className="relative">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as SortBy)}
                className="bg-gray-800 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 appearance-none pr-8"
              >
                <option value="plays">Most Played</option>
                <option value="likes">Most Liked</option>
                <option value="trending">Trending</option>
                <option value="newest">Newest</option>
              </select>
              <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
            </div>

            {/* Track Count */}
            <span className="text-gray-400 text-sm">
              {tracks.length} tracks {hasMore && '(loading more...)'}
            </span>
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center space-x-2 bg-gray-800 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'grid' ? 'bg-purple-600 text-white' : 'text-gray-400 hover:text-white'
              }`}
            >
              <Grid3X3 className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'list' ? 'bg-purple-600 text-white' : 'text-gray-400 hover:text-white'
              }`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* All Tracks Display */}
        {tracks.length > 0 ? (
          <div>
            <h2 className="text-xl font-semibold text-white mb-4">All Popular Tracks</h2>
            <div className={
              viewMode === 'grid' 
                ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                : 'space-y-3'
            }>
              {tracks.map((track, index) => (
                <div key={track.id} className="relative">
                  {viewMode === 'list' && (
                    <div className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 font-bold text-sm z-10">
                      #{index + 1}
                    </div>
                  )}
                  <TrackCard
                    track={track}
                    layout={viewMode === 'grid' ? 'vertical' : 'horizontal'}
                    size={viewMode === 'grid' ? 'md' : 'sm'}
                    showArtwork={true}
                    showStats={true}
                    showActions={true}
                    className={viewMode === 'list' ? 'pl-8' : ''}
                  />
                </div>
              ))}
            </div>
          </div>
        ) : loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
            <span className="ml-3 text-gray-400">Loading popular tracks...</span>
          </div>
        ) : (
          <div className="text-center py-12">
            <TrendingUp className="w-16 h-16 text-gray-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">No popular tracks found</h3>
            <p className="text-gray-400">Check back later for trending content.</p>
          </div>
        )}

        {/* Loading More Indicator */}
        {isFetching && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-500"></div>
            <span className="ml-3 text-gray-400">Loading more tracks...</span>
          </div>
        )}
      </div>
    </MainLayout>
  )
} 