import Link from 'next/link'
import { <PERSON>, ArrowR<PERSON>, Sparkles, Users, Headphones } from 'lucide-react'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-950 to-blue-950 flex items-center justify-center p-4">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 w-full max-w-6xl text-center">
        {/* Header */}
        <header className="mb-12">
          <div className="flex items-center justify-center mb-6" role="banner">
            <div className="p-4 bg-gradient-to-r from-purple-500 to-blue-500 rounded-3xl shadow-2xl">
              <Music className="w-12 h-12 text-white" aria-hidden="true" />
            </div>
          </div>
          <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent mb-6">
            Welcome to Tunami
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
            Discover, create, and share AI-generated music with our intelligent platform.
            Join thousands of music lovers exploring the future of sound.
          </p>
        </header>

        {/* Action Buttons */}
        <nav className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16" role="navigation" aria-label="Main actions">
          <Link
            href="/auth/login"
            className="group w-full sm:w-auto bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900"
            aria-label="Sign in to your account"
          >
            <span className="flex items-center gap-2">
              Sign In
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" aria-hidden="true" />
            </span>
          </Link>
          <Link
            href="/auth/signup"
            className="group w-full sm:w-auto bg-transparent border-2 border-purple-500 hover:bg-purple-500 text-purple-400 hover:text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900"
            aria-label="Create a new account"
          >
            <span className="flex items-center gap-2">
              Get Started
              <Sparkles className="w-5 h-5 group-hover:rotate-12 transition-transform" aria-hidden="true" />
            </span>
          </Link>
        </nav>

        {/* Features Preview */}
        <section className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16" aria-labelledby="features-heading">
          <h2 id="features-heading" className="sr-only">Platform Features</h2>

          <article className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700 hover:border-purple-500/50 transition-colors group">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-purple-500/20 rounded-lg group-hover:bg-purple-500/30 transition-colors">
                <Sparkles className="w-6 h-6 text-purple-400" aria-hidden="true" />
              </div>
              <h3 className="text-xl font-bold text-white">Smart Music Discovery</h3>
            </div>
            <p className="text-gray-300 leading-relaxed">
              AI-powered recommendations tailored to your unique taste and listening habits
            </p>
          </article>

          <article className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700 hover:border-blue-500/50 transition-colors group">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-blue-500/20 rounded-lg group-hover:bg-blue-500/30 transition-colors">
                <Headphones className="w-6 h-6 text-blue-400" aria-hidden="true" />
              </div>
              <h3 className="text-xl font-bold text-white">High-Quality Streaming</h3>
            </div>
            <p className="text-gray-300 leading-relaxed">
              Crystal clear audio with adaptive quality and seamless playback across devices
            </p>
          </article>

          <article className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700 hover:border-green-500/50 transition-colors group">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-green-500/20 rounded-lg group-hover:bg-green-500/30 transition-colors">
                <Users className="w-6 h-6 text-green-400" aria-hidden="true" />
              </div>
              <h3 className="text-xl font-bold text-white">Social Music Experience</h3>
            </div>
            <p className="text-gray-300 leading-relaxed">
              Connect with fellow music lovers, share discoveries, and build communities
            </p>
          </article>
        </section>

        {/* Call to Action */}
        <section className="mt-20 bg-gradient-to-r from-purple-600/20 to-blue-600/20 rounded-2xl p-8 border border-purple-500/30">
          <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">
            Ready to Experience the Future of Music?
          </h2>
          <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
            Join our community and start discovering AI-generated music that matches your vibe perfectly.
          </p>
          <Link
            href="/auth/signup"
            className="inline-flex items-center gap-2 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900"
            aria-label="Start your musical journey"
          >
            Start Your Journey
            <ArrowRight className="w-5 h-5" aria-hidden="true" />
          </Link>
        </section>
      </div>
    </div>
  )
}