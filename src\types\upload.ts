// Upload-related type definitions for the Tunami music platform

export interface UploadFile {
  id: string
  file: File
  name: string
  size: number
  type: string
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error'
  progress: number
  preview?: string
  error?: string
}

export interface UploadMetadata {
  title: string
  artist: string
  album?: string
  genre?: string
  description?: string
  tags: string[]
  aiTool?: string
  isPublic: boolean
  coverArt?: File
  lyrics?: string
  bpm?: number
  key?: string
  duration?: number
}

export interface UploadProgress {
  id: string
  fileName: string
  fileSize: number
  uploadedBytes: number
  progress: number // 0-100
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'failed' | 'cancelled'
  error?: string
  estimatedTimeRemaining?: number
  uploadSpeed?: number // bytes per second
}

export interface UploadResult {
  success: boolean
  trackId?: string
  fileName: string
  fileSize: number
  duration?: number
  error?: string
  warnings?: string[]
  metadata?: {
    title?: string
    artist?: string
    album?: string
    genre?: string
    year?: number
    bitrate?: number
    sampleRate?: number
    channels?: number
  }
}

export interface UploadOptions {
  onProgress?: (progress: UploadProgress) => void
  onComplete?: (result: UploadResult) => void
  onError?: (error: string) => void
  preserveMetadata?: boolean
  generateThumbnail?: boolean
  enableChunkedUpload?: boolean
}

export interface UploadValidation {
  isValid: boolean
  errors: string[]
  warnings: string[]
  fileInfo?: {
    format: string
    duration: number
    bitrate: number
    sampleRate: number
    channels: number
    size: number
  }
}

export interface BulkUploadProgress {
  totalFiles: number
  completedFiles: number
  failedFiles: number
  currentFile?: string
  overallProgress: number
  estimatedTimeRemaining?: number
  errors: Array<{
    fileName: string
    error: string
  }>
}

export type UploadStep = 'files' | 'terms' | 'metadata' | 'uploading' | 'success'

export interface UploadState {
  files: UploadFile[]
  metadata: UploadMetadata
  currentStep: UploadStep
  uploadProgress: Record<string, UploadProgress>
  uploadResults: UploadResult[]
  isSubmitting: boolean
  error: string | null
  hasAcceptedTerms: boolean
  showTermsReminder: boolean
}

// Audio metadata extracted from files
export interface AudioMetadata {
  title?: string
  artist?: string
  album?: string
  albumArtist?: string
  genre?: string
  year?: number
  trackNumber?: number
  discNumber?: number
  duration?: number
  bitrate?: number
  sampleRate?: number
  channels?: number
  encoder?: string
}

// Upload service configuration
export interface UploadConfig {
  maxFileSize: number // bytes
  allowedFormats: string[]
  maxConcurrentUploads: number
  chunkSize: number // bytes
  autoRetry: boolean
  maxRetries: number
}

// File validation result
export interface FileValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  metadata?: AudioMetadata
}

// User upload statistics
export interface UserUploadStats {
  totalTracks: number
  totalSize: number
  publicTracks: number
  privateTracks: number
}

export interface UploadQueueItem {
  id: string
  file: File
  options?: UploadOptions
  status: UploadStatus
  progress?: UploadProgress
  result?: UploadResult
  retryCount: number
  addedAt: Date
}

export type UploadStatus = 'pending' | 'uploading' | 'processing' | 'completed' | 'failed' | 'cancelled'

export interface BatchUploadProgress {
  totalFiles: number
  completedFiles: number
  failedFiles: number
  currentFile?: UploadProgress
  overallProgress: number // 0-100
}

export interface UploadSession {
  id: string
  fileName: string
  fileSize: number
  totalChunks: number
  uploadedChunks: number[]
  createdAt: Date
  expiresAt: Date
  status: 'active' | 'completed' | 'expired' | 'cancelled'
}

// All types are already exported above with their declarations
// No need for re-export as it creates conflicts