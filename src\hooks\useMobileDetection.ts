'use client'

import { useState, useEffect } from 'react'

interface MobileDetection {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  isIOS: boolean
  isAndroid: boolean
  isSafari: boolean
  isChrome: boolean
  isFirefox: boolean
  isTouchDevice: boolean
  screenSize: 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  orientation: 'portrait' | 'landscape'
  devicePixelRatio: number
  platform: string
  userAgent: string
  hasHover: boolean
  hasPointer: boolean
  prefersReducedMotion: boolean
  supportsWebP: boolean
  supportsAVIF: boolean
  viewportWidth: number
  viewportHeight: number
}

export function useMobileDetection(): MobileDetection {
  const [detection, setDetection] = useState<MobileDetection>({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    isIOS: false,
    isAndroid: false,
    isSafari: false,
    isChrome: false,
    isFirefox: false,
    isTouchDevice: false,
    screenSize: 'lg',
    orientation: 'landscape',
    devicePixelRatio: 1,
    platform: '',
    userAgent: '',
    hasHover: true,
    hasPointer: true,
    prefersReducedMotion: false,
    supportsWebP: false,
    supportsAVIF: false,
    viewportWidth: 1920,
    viewportHeight: 1080
  })

  const detectDevice = () => {
    if (typeof window === 'undefined') return detection

    const userAgent = navigator.userAgent.toLowerCase()
    const platform = navigator.platform?.toLowerCase() || ''
    
    // Screen dimensions
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    const devicePixelRatio = window.devicePixelRatio || 1
    
    // Device type detection
    const isMobile = /android|webos|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent) ||
                     (viewportWidth <= 768 && 'ontouchstart' in window)
    
    const isTablet = /ipad|android(?!.*mobile)|tablet|kindle|silk/i.test(userAgent) ||
                     (viewportWidth > 768 && viewportWidth <= 1024 && 'ontouchstart' in window)
    
    const isDesktop = !isMobile && !isTablet
    
    // Operating system detection
    const isIOS = /ip(ad|hone|od)/i.test(userAgent) || 
                  (platform.includes('mac') && 'ontouchstart' in window)
    
    const isAndroid = /android/i.test(userAgent)
    
    // Browser detection
    const isSafari = /safari/i.test(userAgent) && !/chrome|chromium|crios|fxios/i.test(userAgent)
    const isChrome = /chrome|chromium|crios/i.test(userAgent)
    const isFirefox = /firefox|fxios/i.test(userAgent)
    
    // Touch capability
    const isTouchDevice = 'ontouchstart' in window || 
                          navigator.maxTouchPoints > 0 ||
                          (navigator as any).msMaxTouchPoints > 0
    
    // Screen size categories
    let screenSize: 'sm' | 'md' | 'lg' | 'xl' | '2xl' = 'lg'
    if (viewportWidth < 640) screenSize = 'sm'
    else if (viewportWidth < 768) screenSize = 'md'
    else if (viewportWidth < 1024) screenSize = 'lg'
    else if (viewportWidth < 1280) screenSize = 'xl'
    else screenSize = '2xl'
    
    // Orientation
    const orientation = viewportWidth > viewportHeight ? 'landscape' : 'portrait'
    
    // Media query capabilities
    const hasHover = window.matchMedia('(hover: hover)').matches
    const hasPointer = window.matchMedia('(pointer: fine)').matches
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches
    
    // Format support detection
    const supportsWebP = checkWebPSupport()
    const supportsAVIF = checkAVIFSupport()
    
    return {
      isMobile,
      isTablet,
      isDesktop,
      isIOS,
      isAndroid,
      isSafari,
      isChrome,
      isFirefox,
      isTouchDevice,
      screenSize,
      orientation,
      devicePixelRatio,
      platform,
      userAgent,
      hasHover,
      hasPointer,
      prefersReducedMotion,
      supportsWebP,
      supportsAVIF,
      viewportWidth,
      viewportHeight
    }
  }

  const checkWebPSupport = (): boolean => {
    if (typeof window === 'undefined') return false
    
    try {
      const canvas = document.createElement('canvas')
      canvas.width = 1
      canvas.height = 1
      return canvas.toDataURL('image/webp').indexOf('webp') > -1
    } catch {
      return false
    }
  }

  const checkAVIFSupport = (): boolean => {
    if (typeof window === 'undefined') return false
    
    try {
      const canvas = document.createElement('canvas')
      canvas.width = 1
      canvas.height = 1
      return canvas.toDataURL('image/avif').indexOf('avif') > -1
    } catch {
      return false
    }
  }

  useEffect(() => {
    // Initial detection
    const initialDetection = detectDevice()
    setDetection(initialDetection)

    // Listen for resize events
    const handleResize = () => {
      const newDetection = detectDevice()
      setDetection(newDetection)
    }

    // Listen for orientation change
    const handleOrientationChange = () => {
      // Delay to ensure dimensions are updated
      setTimeout(() => {
        const newDetection = detectDevice()
        setDetection(newDetection)
      }, 100)
    }

    // Listen for media query changes
    const mediaQueries = [
      window.matchMedia('(hover: hover)'),
      window.matchMedia('(pointer: fine)'),
      window.matchMedia('(prefers-reduced-motion: reduce)')
    ]

    const handleMediaQueryChange = () => {
      const newDetection = detectDevice()
      setDetection(newDetection)
    }

    // Add event listeners
    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleOrientationChange)
    
    mediaQueries.forEach(mq => {
      mq.addEventListener('change', handleMediaQueryChange)
    })

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleOrientationChange)
      
      mediaQueries.forEach(mq => {
        mq.removeEventListener('change', handleMediaQueryChange)
      })
    }
  }, [])

  return detection
}

// Additional utility functions
export function isMobileDevice(): boolean {
  if (typeof window === 'undefined') return false
  
  const userAgent = navigator.userAgent.toLowerCase()
  return /android|webos|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent) ||
         (window.innerWidth <= 768 && 'ontouchstart' in window)
}

export function isTabletDevice(): boolean {
  if (typeof window === 'undefined') return false
  
  const userAgent = navigator.userAgent.toLowerCase()
  return /ipad|android(?!.*mobile)|tablet|kindle|silk/i.test(userAgent) ||
         (window.innerWidth > 768 && window.innerWidth <= 1024 && 'ontouchstart' in window)
}

export function isDesktopDevice(): boolean {
  return !isMobileDevice() && !isTabletDevice()
}

export function isIOSDevice(): boolean {
  if (typeof window === 'undefined') return false
  
  const userAgent = navigator.userAgent.toLowerCase()
  const platform = navigator.platform?.toLowerCase() || ''
  
  return /ip(ad|hone|od)/i.test(userAgent) || 
         (platform.includes('mac') && 'ontouchstart' in window)
}

export function isAndroidDevice(): boolean {
  if (typeof window === 'undefined') return false
  
  return /android/i.test(navigator.userAgent.toLowerCase())
}

export function isSafariBrowser(): boolean {
  if (typeof window === 'undefined') return false

  const userAgent = navigator.userAgent.toLowerCase()
  return /safari/i.test(userAgent) && !/chrome|chromium|crios|fxios/i.test(userAgent)
}

export function isSafariMobile(): boolean {
  if (typeof window === 'undefined') return false

  const userAgent = navigator.userAgent.toLowerCase()
  return isIOSDevice() && /safari/i.test(userAgent) && !/chrome|chromium|crios|fxios/i.test(userAgent)
}

export function isChromeBasedBrowser(): boolean {
  if (typeof window === 'undefined') return false
  
  return /chrome|chromium|crios/i.test(navigator.userAgent.toLowerCase())
}

export function supportsTouch(): boolean {
  if (typeof window === 'undefined') return false
  
  return 'ontouchstart' in window || 
         navigator.maxTouchPoints > 0 ||
         (navigator as any).msMaxTouchPoints > 0
}

export function getScreenSize(): 'sm' | 'md' | 'lg' | 'xl' | '2xl' {
  if (typeof window === 'undefined') return 'lg'
  
  const width = window.innerWidth
  
  if (width < 640) return 'sm'
  if (width < 768) return 'md'
  if (width < 1024) return 'lg'
  if (width < 1280) return 'xl'
  return '2xl'
}

export function getOrientation(): 'portrait' | 'landscape' {
  if (typeof window === 'undefined') return 'landscape'
  
  return window.innerWidth > window.innerHeight ? 'landscape' : 'portrait'
}

export function getDevicePixelRatio(): number {
  if (typeof window === 'undefined') return 1
  
  return window.devicePixelRatio || 1
}

export function hasHoverCapability(): boolean {
  if (typeof window === 'undefined') return true
  
  return window.matchMedia('(hover: hover)').matches
}

export function hasFinePointer(): boolean {
  if (typeof window === 'undefined') return true
  
  return window.matchMedia('(pointer: fine)').matches
}

export function prefersReducedMotion(): boolean {
  if (typeof window === 'undefined') return false
  
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches
}

export function getViewportDimensions(): { width: number; height: number } {
  if (typeof window === 'undefined') return { width: 1920, height: 1080 }
  
  return {
    width: window.innerWidth,
    height: window.innerHeight
  }
}

// Device-specific utilities
export function isIPhoneX(): boolean {
  if (typeof window === 'undefined') return false
  
  return isIOSDevice() && 
         window.screen.width === 375 && 
         window.screen.height === 812 &&
         window.devicePixelRatio === 3
}

export function isIPadPro(): boolean {
  if (typeof window === 'undefined') return false
  
  return isIOSDevice() && 
         ((window.screen.width === 1024 && window.screen.height === 1366) ||
          (window.screen.width === 834 && window.screen.height === 1194))
}

export function isStandaloneApp(): boolean {
  if (typeof window === 'undefined') return false
  
  return window.matchMedia('(display-mode: standalone)').matches ||
         (window.navigator as any).standalone === true ||
         document.referrer.includes('android-app://')
}

export function supportsPWA(): boolean {
  if (typeof window === 'undefined') return false
  
  return 'serviceWorker' in navigator && 
         'PushManager' in window &&
         'Notification' in window
}

export function getNetworkType(): string {
  if (typeof navigator === 'undefined' || !('connection' in navigator)) return 'unknown'
  
  const connection = (navigator as any).connection
  return connection?.effectiveType || 'unknown'
}

export function getConnectionSpeed(): number {
  if (typeof navigator === 'undefined' || !('connection' in navigator)) return 0
  
  const connection = (navigator as any).connection
  return connection?.downlink || 0
}

export function isLowEndDevice(): boolean {
  if (typeof navigator === 'undefined') return false
  
  // Check for performance hints
  const deviceMemory = (navigator as any).deviceMemory
  const hardwareConcurrency = navigator.hardwareConcurrency
  
  return deviceMemory ? deviceMemory <= 4 : hardwareConcurrency <= 2
} 