'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Music, Mail, Lock, User, ArrowRight, Eye, EyeOff, Loader2 } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { GoogleIcon, GitHubIcon } from '@/components/icons/SocialIcons'

export default function SignUpPage() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    username: ''
  })
  const [acceptTerms, setAcceptTerms] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [mounted, setMounted] = useState(false)
  const [oauthLoading, setOauthLoading] = useState<'google' | 'github' | null>(null)
  const router = useRouter()
  const { signUp, signInWithGoogle, signInWithGitHub } = useAuth()

  // OAuth handlers - moved before early return to fix hooks rules
  const handleGoogleSignUp = useCallback(async () => {
    if (oauthLoading) return
    
    setOauthLoading('google')
    setError('')
    
    try {
      const { error: oauthError } = await signInWithGoogle({
        redirectTo: `${window.location.origin}/auth/callback`
      })
      
      if (oauthError) {
        setError(oauthError.message || 'Google sign up failed')
        setOauthLoading(null)
      }
      // If successful, user will be redirected to Google OAuth
    } catch (err: any) {
      setError(err.message || 'An unexpected error occurred')
      setOauthLoading(null)
    }
  }, [signInWithGoogle, oauthLoading])

  const handleGitHubSignUp = useCallback(async () => {
    if (oauthLoading) return
    
    setOauthLoading('github')
    setError('')
    
    try {
      const { error: oauthError } = await signInWithGitHub({
        redirectTo: `${window.location.origin}/auth/callback`
      })
      
      if (oauthError) {
        setError(oauthError.message || 'GitHub sign up failed')
        setOauthLoading(null)
      }
      // If successful, user will be redirected to GitHub OAuth
    } catch (err: any) {
      setError(err.message || 'An unexpected error occurred')
      setOauthLoading(null)
    }
  }, [signInWithGitHub, oauthLoading])

  // Fix hydration issue
  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null
  }

  // Enhanced validation function
  const validateForm = () => {
    // Email validation
    if (!formData.email.trim()) {
      return 'Email address is required'
    }
    if (formData.email.length > 254) {
      return 'Email address is too long'
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(formData.email.trim())) {
      return 'Please enter a valid email address'
    }

    // Password validation
    if (!formData.password) {
      return 'Password is required'
    }
    if (formData.password.length < 6) {
      return 'Password must be at least 6 characters long'
    }
    if (formData.password.length > 128) {
      return 'Password is too long (maximum 128 characters)'
    }
    if (!/(?=.*[a-z])/.test(formData.password)) {
      return 'Password must contain at least one lowercase letter'
    }
    if (!/(?=.*\d)/.test(formData.password)) {
      return 'Password must contain at least one number'
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      return 'Please confirm your password'
    }
    if (formData.password !== formData.confirmPassword) {
      return 'Passwords do not match'
    }

    // Username validation (if provided)
    if (formData.username && formData.username.length > 0) {
      if (formData.username.length < 3) {
        return 'Username must be at least 3 characters long'
      }
      if (formData.username.length > 30) {
        return 'Username is too long (maximum 30 characters)'
      }
      if (!/^[a-zA-Z0-9_-]+$/.test(formData.username)) {
        return 'Username can only contain letters, numbers, underscores, and hyphens'
      }
    }

    // Full name validation (if provided)
    if (formData.fullName && formData.fullName.length > 100) {
      return 'Full name is too long (maximum 100 characters)'
    }

    // Terms acceptance
    if (!acceptTerms) {
      return 'Please accept the terms and conditions to continue'
    }

    return null
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setLoading(true)

    // Enhanced validation
    const validationError = validateForm()
    if (validationError) {
      setError(validationError)
      setLoading(false)
      return
    }

    try {
      const { data, error } = await signUp(
        formData.email,
        formData.password,
        {
          data: {
            full_name: formData.fullName,
            username: formData.username
          }
        }
      )

      if (error) {
        console.error('Signup error:', error)
        setError((error as any)?.message || 'Sign up failed')
      } else if (data?.user) {
        // Check if email confirmation is required
        if (data.user.email_confirmed_at) {
          // User is confirmed, redirect to dashboard
          router.push('/dashboard')
        } else {
          // Email confirmation required
          setError('Please check your email and click the confirmation link to complete your registration.')
        }
      } else {
        setError('Sign up failed - no user data received')
      }
    } catch (err) {
      console.error('Signup exception:', err)
      setError('An unexpected error occurred. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-dark-900 via-primary-950 to-secondary-950 flex items-center justify-center p-4">
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-72 h-72 bg-primary-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-secondary-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-accent-500/10 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      <div className="relative z-10 w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl">
              <Music className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent mb-2">
            Join Tunami
          </h1>
          <p className="text-gray-400">
            Create your account and discover amazing music
          </p>
        </div>

        {/* Form */}
        <div className="glass-dark p-8 rounded-2xl border border-gray-700">
          {/* OAuth Buttons */}
          <div className="space-y-3 mb-6">
            <button
              type="button"
              onClick={handleGoogleSignUp}
              disabled={loading || oauthLoading === 'google'}
              className="w-full flex items-center justify-center px-4 py-3 border border-gray-600 rounded-lg bg-white hover:bg-gray-50 text-gray-900 font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {oauthLoading === 'google' ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-900 mr-3"></div>
              ) : (
                <GoogleIcon className="mr-3" size={20} />
              )}
              Sign up with Google
            </button>

            <button
              type="button"
              onClick={handleGitHubSignUp}
              disabled={loading || oauthLoading === 'github'}
              className="w-full flex items-center justify-center px-4 py-3 border border-gray-600 rounded-lg bg-gray-900 hover:bg-gray-800 text-white font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {oauthLoading === 'github' ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
              ) : (
                <GitHubIcon className="mr-3 text-white" size={20} />
              )}
              Sign up with GitHub
            </button>
          </div>

          {/* Divider */}
          <div className="relative mb-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-600"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-dark-900 text-gray-400">Or sign up with email</span>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                <p className="text-red-400 text-sm">{error}</p>
              </div>
            )}

            {/* Full Name */}
            <div>
              <label htmlFor="fullName" className="block text-gray-300 text-sm font-medium mb-2">
                Full Name (Optional)
              </label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  id="fullName"
                  name="fullName"
                  type="text"
                  value={formData.fullName}
                  onChange={handleChange}
                  className="w-full bg-dark-800/50 border border-gray-600 rounded-lg pl-11 pr-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all"
                  placeholder="Enter your full name"
                />
              </div>
            </div>

            {/* Username */}
            <div>
              <label htmlFor="username" className="block text-gray-300 text-sm font-medium mb-2">
                Username (Optional)
              </label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  id="username"
                  name="username"
                  type="text"
                  value={formData.username}
                  onChange={handleChange}
                  className="w-full bg-dark-800/50 border border-gray-600 rounded-lg pl-11 pr-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all"
                  placeholder="Choose a username"
                />
              </div>
            </div>

            {/* Email */}
            <div>
              <label htmlFor="email" className="block text-gray-300 text-sm font-medium mb-2">
                Email Address *
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  id="email"
                  name="email"
                  type="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full bg-dark-800/50 border border-gray-600 rounded-lg pl-11 pr-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all"
                  placeholder="Enter your email"
                />
              </div>
            </div>

            {/* Password */}
            <div>
              <label htmlFor="password" className="block text-gray-300 text-sm font-medium mb-2">
                Password *
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  required
                  value={formData.password}
                  onChange={handleChange}
                  className="w-full bg-dark-800/50 border border-gray-600 rounded-lg pl-11 pr-12 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all"
                  placeholder="Create a password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            {/* Confirm Password */}
            <div>
              <label htmlFor="confirmPassword" className="block text-gray-300 text-sm font-medium mb-2">
                Confirm Password *
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  required
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  className="w-full bg-dark-800/50 border border-gray-600 rounded-lg pl-11 pr-12 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all"
                  placeholder="Confirm your password"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
                >
                  {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            {/* Terms and Conditions */}
            <div className="flex items-start space-x-3">
              <input
                id="acceptTerms"
                type="checkbox"
                checked={acceptTerms}
                onChange={(e) => setAcceptTerms(e.target.checked)}
                className="mt-1 w-4 h-4 text-primary-500 bg-dark-800 border-gray-600 rounded focus:ring-primary-500 focus:ring-2"
              />
              <label htmlFor="acceptTerms" className="text-sm text-gray-300">
                I agree to the{' '}
                <Link href="/terms" className="text-primary-400 hover:text-primary-300">
                  Terms of Service
                </Link>{' '}
                and{' '}
                <Link href="/privacy" className="text-primary-400 hover:text-primary-300">
                  Privacy Policy
                </Link>
              </label>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading}
              className="w-full btn-primary py-3 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <>
                  <span>Create Account</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </form>

          {/* Sign In Link */}
          <div className="mt-8 text-center">
            <p className="text-gray-400 text-sm">
              Already have an account?{' '}
              <Link
                href="/auth/login"
                className="text-primary-400 hover:text-primary-300 font-medium transition-colors"
              >
                Sign in instead
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
} 