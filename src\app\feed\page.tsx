'use client'

import { useEffect, useState } from 'react'
import { Heart, Play, Users, Music, Plus, TrendingUp, Clock } from 'lucide-react'
import { getFollowingActivityFeed } from '@/lib/social'
import { createBrowserClient } from '@/lib/supabase'
import type { ActivityWithDetails } from '@/types/database'
import { Avatar } from '@/components/ui/Avatar'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import Link from 'next/link'

const supabase = createBrowserClient()

export default function ActivityFeedPage() {
  const [activities, setActivities] = useState<ActivityWithDetails[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [currentUser, setCurrentUser] = useState<any>(null)
  const [page, setPage] = useState(0)
  const [hasMore, setHasMore] = useState(true)

  useEffect(() => {
    getCurrentUser()
    loadActivityFeed()
  }, [])

  async function getCurrentUser() {
    const { data: { user } } = await supabase.auth.getUser()
    setCurrentUser(user)
  }

  async function loadActivityFeed(pageNum = 0) {
    try {
      if (pageNum === 0) setIsLoading(true)
      
      const newActivities = await getFollowingActivityFeed(pageNum, 20)
      
      if (pageNum === 0) {
        setActivities(newActivities)
      } else {
        setActivities(prev => [...prev, ...newActivities])
      }
      
      setHasMore(newActivities.length === 20)
      setPage(pageNum)
    } catch (error) {
      console.error('Error loading activity feed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  async function loadMoreActivities() {
    await loadActivityFeed(page + 1)
  }

  if (!currentUser) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <Card className="bg-gray-800/50 border-gray-700 p-8 text-center max-w-md">
          <Users className="w-16 h-16 text-gray-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-white mb-2">Sign In Required</h2>
          <p className="text-gray-400 mb-4">
            You need to be signed in to view your activity feed.
          </p>
          <Link href="/auth">
            <Button>Sign In</Button>
          </Link>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <div className="container mx-auto px-6 py-8 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4 flex items-center justify-center gap-3">
            <TrendingUp className="w-8 h-8" />
            Activity Feed
          </h1>
          <p className="text-gray-300 text-lg">
            Stay updated with your followed users&apos; latest activities
          </p>
        </div>

        {/* Quick Actions */}
        <Card className="bg-gray-800/50 backdrop-blur-sm border-gray-700 p-6 mb-8">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-white">Quick Actions</h2>
            <div className="flex gap-3">
              <Link href="/users">
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  Discover Users
                </Button>
              </Link>
              <Link href="/upload">
                <Button size="sm" className="flex items-center gap-2">
                  <Plus className="w-4 h-4" />
                  Upload Track
                </Button>
              </Link>
            </div>
          </div>
        </Card>

        {/* Activity Feed */}
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <Card key={i} className="bg-gray-800/50 border-gray-700 p-6 animate-pulse">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-gray-600 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-600 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-600 rounded w-1/2 mb-2"></div>
                    <div className="h-16 bg-gray-600 rounded"></div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        ) : activities.length > 0 ? (
          <>
            <div className="space-y-4">
              {activities.map((activity) => (
                <ActivityFeedCard key={activity.id} activity={activity} />
              ))}
            </div>

            {hasMore && (
              <div className="text-center mt-8">
                <Button
                  onClick={loadMoreActivities}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Clock className="w-4 h-4" />
                  Load More Activities
                </Button>
              </div>
            )}
          </>
        ) : (
          <Card className="bg-gray-800/50 border-gray-700 p-12 text-center">
            <TrendingUp className="w-16 h-16 text-gray-500 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">No Activities Yet</h3>
            <p className="text-gray-400 mb-6">
              Follow some users to see their activities in your feed
            </p>
            <Link href="/users">
              <Button className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                Discover Users
              </Button>
            </Link>
          </Card>
        )}
      </div>
    </div>
  )
}

function ActivityFeedCard({ activity }: { activity: ActivityWithDetails }) {
  const getActivityIcon = () => {
    switch (activity.activity_type) {
      case 'track_upload':
        return <Music className="w-5 h-5 text-blue-400" />
      case 'playlist_create':
        return <Play className="w-5 h-5 text-green-400" />
      case 'track_like':
        return <Heart className="w-5 h-5 text-red-400" />
      case 'user_follow':
        return <Users className="w-5 h-5 text-purple-400" />
      case 'track_mention':
        return <Music className="w-5 h-5 text-yellow-400" />
      case 'playlist_share':
        return <Play className="w-5 h-5 text-indigo-400" />
      default:
        return <TrendingUp className="w-5 h-5 text-gray-400" />
    }
  }

  const getActivityMessage = () => {
    switch (activity.activity_type) {
      case 'track_upload':
        return (
          <>
            uploaded a new track{' '}
            {activity.track_title && (
              <Link href={`/track/${activity.track_id}`} className="text-blue-400 hover:underline">
                &quot;{activity.track_title}&quot;
              </Link>
            )}
          </>
        )
      case 'playlist_create':
        return (
          <>
            created a new playlist{' '}
            {activity.playlist_name && (
              <Link href={`/playlist/${activity.playlist_id}`} className="text-green-400 hover:underline">
                &quot;{activity.playlist_name}&quot;
              </Link>
            )}
          </>
        )
      case 'track_like':
        return (
          <>
            liked{' '}
            {activity.track_title && (
              <Link href={`/track/${activity.track_id}`} className="text-red-400 hover:underline">
                &quot;{activity.track_title}&quot;
              </Link>
            )}
          </>
        )
      case 'user_follow':
        return (
          <>
            started following{' '}
            {activity.target_user_username && (
              <Link href={`/users/${activity.target_user_username}`} className="text-purple-400 hover:underline">
                {activity.target_user_name || activity.target_user_username}
              </Link>
            )}
          </>
        )
      case 'track_mention':
        return (
          <>
            mentioned{' '}
            {activity.target_user_username && (
              <Link href={`/users/${activity.target_user_username}`} className="text-yellow-400 hover:underline">
                {activity.target_user_name || activity.target_user_username}
              </Link>
            )}
            {' '}in{' '}
            {activity.track_title && (
              <Link href={`/track/${activity.track_id}`} className="text-yellow-400 hover:underline">
                &quot;{activity.track_title}&quot;
              </Link>
            )}
          </>
        )
      case 'playlist_share':
        return (
          <>
            shared playlist{' '}
            {activity.playlist_name && (
              <Link href={`/playlist/${activity.playlist_id}`} className="text-indigo-400 hover:underline">
                &quot;{activity.playlist_name}&quot;
              </Link>
            )}
          </>
        )
      default:
        return 'had some activity'
    }
  }

  const getTimeAgo = (dateString: string) => {
    const now = new Date()
    const activityDate = new Date(dateString)
    const diffMs = now.getTime() - activityDate.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffMins < 1) return 'just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    if (diffDays < 7) return `${diffDays}d ago`
    return activityDate.toLocaleDateString()
  }

  return (
    <Card className="bg-gray-800/50 border-gray-700 p-6 hover:bg-gray-700/30 transition-colors">
      <div className="flex items-start gap-4">
        <Link href={`/users/${activity.user_username}`}>
          <Avatar
            src={activity.user_avatar_url}
            alt={activity.user_name || activity.user_username || ''}
            size="md"
            className="hover:ring-2 hover:ring-blue-400 transition-all"
          />
        </Link>
        
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            {getActivityIcon()}
            <p className="text-gray-300">
              <Link 
                href={`/users/${activity.user_username}`}
                className="font-semibold text-white hover:text-blue-400 transition-colors"
              >
                {activity.user_name || activity.user_username}
              </Link>{' '}
              {getActivityMessage()}
            </p>
          </div>
          
          <p className="text-gray-500 text-sm">
            {getTimeAgo(activity.created_at)}
          </p>

          {/* Activity Content Preview */}
          {(activity.track_id || activity.playlist_id) && (
            <div className="mt-3 p-3 bg-gray-700/30 rounded-lg">
              {activity.track_id && (
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                    <Music className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <p className="text-white font-medium">{activity.track_title}</p>
                    <p className="text-gray-400 text-sm">Track</p>
                  </div>
                </div>
              )}
              
              {activity.playlist_id && (
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-teal-500 rounded-lg flex items-center justify-center">
                    <Play className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <p className="text-white font-medium">{activity.playlist_name}</p>
                    <p className="text-gray-400 text-sm">Playlist</p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </Card>
  )
} 